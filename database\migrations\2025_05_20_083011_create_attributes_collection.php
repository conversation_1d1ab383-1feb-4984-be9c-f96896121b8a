<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use MongoDB\Laravel\Schema\Blueprint;

/**
 * Migration for creating the attributes collection in MongoDB.
 * This collection stores product attributes in a hierarchical structure.
 * - Parent attributes (parent_id = null) represent attribute types like color, size, etc.
 * - Child attributes (with parent_id) represent attribute values like red, XL, etc.
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('attributes', function (Blueprint $collection) {
            $collection->string('parent_id')->nullable();
            $collection->string('title');
            $collection->string('value')->nullable();
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('attributes');
    }
};
