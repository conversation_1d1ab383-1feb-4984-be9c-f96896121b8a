<?php

namespace App\Traits\Address;

use App\Models\User\User;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * Address Relations Trait
 *
 * This trait contains all relationship methods for the Address model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Address
 */
trait AddressRelationsTrait
{
    /**
     * Get the user that owns this address.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
