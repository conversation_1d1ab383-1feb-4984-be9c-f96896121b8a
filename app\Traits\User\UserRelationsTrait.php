<?php

namespace App\Traits\User;

use App\Models\User\Address;
use App\Models\Shopping\Invoice;
use App\Models\User\Shop;
use App\Models\Shopping\ShoppingCart;
use App\Models\Shopping\Transaction;
use App\Models\User\WalletTransaction;
use MongoDB\Laravel\Relations\BelongsToMany;
use MongoDB\Laravel\Relations\HasMany;
use MongoDB\Laravel\Relations\HasOne;
use MongoDB\Laravel\Relations\MorphMany;

/**
 * User Relations Trait
 *
 * This trait contains all relationship methods for the User model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\User
 */
trait UserRelationsTrait
{
    /**
     * Get the shops that this user belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsToMany
     */
    public function shops(): BelongsToMany
    {
        return $this->belongsToMany(Shop::class, 'shop_user', "user_id", "shop_id");
    }

    /**
     * Get the invoices that belong to this user.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the addresses that belong to this user.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(Address::class);
    }

    /**
     * Get the ShoppingCart that belong to this user.
     *
     * @return \MongoDB\Laravel\Relations\HasOne
     */
    public function cart(): HasOne
    {
        return $this->hasOne(ShoppingCart::class);
    }

    /**
     * Get the wallet transactions that belong to this user.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get the payment transactions associated with this user (for wallet top-ups).
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'payable');
    }
}
