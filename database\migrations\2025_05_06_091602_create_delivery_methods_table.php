<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('delivery_methods', function ($collection) {
            $collection->string('title');
            $collection->double('price');
            $collection->boolean('active')->default(true);
            // If shop_id is null, the delivery method is global (available to all shops)
            // If shop_id has a value, the delivery method is specific to that shop only
            $collection->string('shop_id')->nullable();
            // URL to the delivery method's image (e.g., shipping company logo)
            $collection->string('image_url')->nullable();
            $collection->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_methods');
    }
};
