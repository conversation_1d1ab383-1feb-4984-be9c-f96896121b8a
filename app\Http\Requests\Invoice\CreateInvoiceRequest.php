<?php

namespace App\Http\Requests\Invoice;

use App\Models\Product\ProductVariation;
use App\Models\Shopping\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Create Invoice Request
 *
 * Validates the request data for creating a new invoice.
 */
class CreateInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can create invoices
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

            'address_id' => [
                'required',
                'string',
                Rule::exists('addresses', 'id')->where(function ($query) {
                    $query->where('user_id', auth()->id());
                }),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'address_id.required' => __('messages.invoice.address_required'),
            'address_id.exists' => __('messages.invoice.address_not_found'),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $userId = auth()->id();

            // Check if the user's cart has items
            $cart = ShoppingCart::where('user_id', $userId)->first();

            // If cart doesn't exist or has no items, add error
            if (!$cart || $cart->items()->count() === 0) {
                $validator->errors()->add('cart', __('messages.cart.cart_empty'));
                return;
            }

            // Check stock availability for each cart item
            $cartItems = $cart->items;
            foreach ($cartItems as $cartItem) {
                // Get the product variant
                $variant = ProductVariation::find($cartItem->variant_id);

                // If variant doesn't exist, add error
                if (!$variant) {
                    $validator->errors()->add('variant', __('messages.cart.variant_not_found'));
                    return;
                }

                // Check if there's enough stock
                if ($variant->getCurrentQuantityAttribute() < $cartItem->quantity) {
                    $validator->errors()->add('quantity', __('messages.cart.insufficient_stock'));
                    return;
                }
            }
        });
    }

    // No need to prepare data for validation as we'll get the user ID directly in the action
}
