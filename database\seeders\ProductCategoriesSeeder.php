<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\Category;

/**
 * Seeder for assigning categories to products.
 * 
 * This seeder ensures that all products have at least one category assigned,
 * which is necessary for the product recommendation functionality to work properly.
 */
class ProductCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Assigns random categories to products that don't have any categories yet.
     */
    public function run(): void
    {
        // Get all products
        $products = Product::all();
        
        // Get all categories
        $categories = Category::all();
        
        if ($categories->isEmpty()) {
            $this->command->warn('No categories found. Make sure to run CategorySeeder first.');
            return;
        }
        
        $this->command->info('Assigning categories to products...');
        
        foreach ($products as $product) {
            // Check if the product already has categories
            $existingCategories = $product->categories()->count();
            
            if ($existingCategories === 0) {
                // Assign 1-3 random categories to the product
                $randomCategories = $categories->random(rand(1, 3));
                
                foreach ($randomCategories as $category) {
                    // Attach the category to the product
                    $product->categories()->attach($category->_id);
                }
                
                $this->command->info("Assigned categories to product: {$product->title}");
            } else {
                $this->command->info("Product already has categories: {$product->title}");
            }
        }
        
        $this->command->info('Finished assigning categories to products.');
    }
}
