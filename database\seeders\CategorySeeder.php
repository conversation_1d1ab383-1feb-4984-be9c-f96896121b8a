<?php

namespace Database\Seeders;

use App\Models\Product\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

/**
 * Seeder for creating product categories in the database.
 */
class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates categories with Persian names and assigns them types.
     * Also creates specific required categories needed for other seeders.
     */
    public function run(): void
    {
        $types = ['product'];

        $categoryNames = [
            'الکترونیک',
            'پوشاک',
            'لوازم خانگی',
            'کتاب و لوازم التحریر',
            'ورزشی',
            'زیبایی و سلامت',
            'اسباب بازی',
            'خودرو',
            'ابزار',
            'سفر',
            'موبایل',
            'کامپیوتر'
        ];

        foreach ($categoryNames as $title) {
            $slug = Str::slug($title);
            $type = $types[array_rand($types)];

            Category::updateOrCreate(
                ['slug' => $slug],
                [
                    'title' => $title,
                    'type' => $type,
                    'slug' => $slug,
                ]
            );
        }

        // Create a few specific categories needed for other seeders
        $requiredCategories = [
            [
                'title' => 'پوشاک مردانه',
                'type' => 'محصول',
                'slug' => 'mens-wear'
            ],
            [
                'title' => 'کالکشن تابستانه',
                'type' => 'محصول',
                'slug' => 'summer-collection'
            ],
            [
                'title' => 'لوازم جانبی',
                'type' => 'محصول',
                'slug' => 'accessories'
            ]
        ];

        foreach ($requiredCategories as $category) {
            Category::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }

        $this->command->info('دسته‌بندی‌ها با موفقیت ایجاد شدند.');
    }
}
