<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('comments', function ($collection) {
            $collection->string('commentable_id');
            $collection->string('commentable_type');
            $collection->string('user_id')->nullable();
            $collection->string('parent_id')->nullable();
            $collection->string('body');
            $collection->string('ip_address')->nullable();
            $collection->string('user_agent')->nullable();
            // has_bought is only used when the comment is for a product
            $collection->boolean('has_bought')->nullable();
            // rate is only used when the comment is for a product (values from 1 to 5)
            $collection->integer('rate')->nullable();
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->drop('comments');
    }
};