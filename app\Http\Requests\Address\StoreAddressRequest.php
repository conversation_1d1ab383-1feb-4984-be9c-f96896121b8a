<?php

namespace App\Http\Requests\Address;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Store Address Request
 *
 * Validates the request data for creating a new address.
 */
class StoreAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can create addresses
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

            'name' => 'required|string',
            'receiver_name' => 'required|string',
            'receiver_phone' => 'required|string',
            'is_recipient_self' => 'sometimes|boolean',
            'province' => 'required|string',
            'city' => 'required|string',
            'zip_code' => 'required|string',
            'address' => 'required|string',
            'latitude' => 'sometimes|numeric',
            'longitude' => 'sometimes|numeric',
        ];
    }

    // Custom validation messages are now handled by lang/fa/validation.php

    // No need to prepare data for validation as we'll get the user ID directly in the action
}
