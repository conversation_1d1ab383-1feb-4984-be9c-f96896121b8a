<?php

namespace App\Traits\Category;

use Illuminate\Support\Str;

/**
 * Category Attributes Trait
 *
 * This trait contains all attribute methods for the Category model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Category
 */
trait CategoryAttributesTrait
{
    /**
     * Boot the trait.
     * Automatically generate slug on creating if not provided.
     *
     * @return void
     */
    protected static function bootCategoryAttributesTrait(): void
    {
        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->title);
            }
        });
    }
}
