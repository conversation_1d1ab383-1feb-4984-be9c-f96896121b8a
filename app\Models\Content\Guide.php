<?php

namespace App\Models\Content;

use App\Traits\Guide\GuideRelationsTrait;
use MongoDB\Laravel\Eloquent\Model;

/**
 * Guide Model
 *
 * Represents a guide with rich HTML content that can be associated with products
 * through a polymorphic relationship. Used for storing instructional content.
 *
 * @property string $_id MongoDB document ID
 * @property string $title The title of the guide
 * @property string $content The HTML content of the guide
 * @property int $order The display order of the guide (lower numbers appear first)
 * @property string $guideable_type The class name of the parent model
 * @property string $guideable_id The ID of the parent model
 * @property \Carbon\Carbon $created_at When the guide was created
 * @property \Carbon\Carbon $updated_at When the guide was last updated
 */
class Guide extends Model
{
    use GuideRelationsTrait;
    /**
     * The database connection used by the model.
     *
     * @var string
     */
    protected $connection = 'mongodb';

    /**
     * The collection associated with the model.
     *
     * @var string
     */
    protected $collection = 'guides';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'title',
        'content',
        'order',
        'guideable_type',
        'guideable_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'order' => 'integer',
        'guideable_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


}
