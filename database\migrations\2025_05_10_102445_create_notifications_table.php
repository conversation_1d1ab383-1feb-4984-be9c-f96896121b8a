<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('notifications', function ($collection) {
            $collection->string('title');
            $collection->string('description');
            $collection->string('image');
            $collection->json('payload');
            $collection->timestamp('time_to_send');
            $collection->timestamp('sent_time');
            $collection->string('type');
            $collection->string('target');
            $collection->string('status');
            $collection->string('error')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
