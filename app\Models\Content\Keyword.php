<?php

namespace App\Models\Content;

use App\Traits\Keyword\KeywordRelationsTrait;
use MongoDB\Laravel\Eloquent\Model;

class Keyword extends Model
{
    use KeywordRelationsTrait;
    protected $connection = 'mongodb';
    protected $collection = 'keywords';

    protected $casts = [
        '_id' => 'string',
        'keyword_id' => 'string',
        'keywordable_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $fillable = ['name'];

}
