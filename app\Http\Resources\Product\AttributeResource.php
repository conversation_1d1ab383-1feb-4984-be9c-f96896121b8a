<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Attribute models into API responses.
 */
class AttributeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'title' => $this->title,
        ];

        // Include value only if it's not null
        if ($this->value !== null) {
            $data['value'] = $this->value;
        }



        // Include values collection if this is a parent attribute and values are loaded
        if ($this->relationLoaded('values')) {
            $data['values'] = AttributeResource::collection($this->values);
        }

        return $data;
    }
}
