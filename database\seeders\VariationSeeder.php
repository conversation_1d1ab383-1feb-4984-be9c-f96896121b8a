<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

/**
 * Seeder for creating product variations for the cloth product.
 */
class VariationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates variations for the cloth product with:
     * - Combinations of sizes (X, XL) and colors (red, blue)
     * - Random prices
     * - Size and color attributes with appropriate metadata
     */
    public function run(): void
    {
        $product = Product::firstOrCreate([
            'slug' => 'cloth',
        ], [
            'title' => 'پارچه',
            'description' => 'محصول پارچه با کیفیت عالی',
            'meta_title' => 'پارچه با کیفیت',
            'meta_description' => 'پارچه با کیفیت عالی برای انواع لباس',
        ]);

        $sizes = ['X', 'XL'];
        $colors = [
            ['title' => 'قرمز', 'extra_data' => ['hex' => '#ff0000']],
            ['title' => 'آبی', 'extra_data' => ['hex' => '#0000ff']],
        ];

        foreach ($sizes as $size) {
            foreach ($colors as $color) {
                $variation = ProductVariation::create([
                    'product_id' => (string) $product->_id,
                    'sku' => strtoupper("CLOTH-{$size}-{$color['title']}"),
                    'price' => generateRandomFakeprice()
                ]);

                $variation->attributes()->createMany([
                    [
                        'attribute_title' => 'سایز',
                        'attribute_value' => $size,
                        'attribute_type' => 'size',
                    ],
                    [
                        'attribute_title' => 'رنگ',
                        'attribute_value' => $color['title'],
                        'attribute_type' => 'color',
                        'extra_data' => $color['extra_data'],
                    ]
                ]);
            }
        }
    }
}

