<?php

namespace App\Models\Product;

use App\Traits\ProductVariation\ProductVariationRelationsTrait;
use App\Traits\ProductVariation\ProductVariationAttributesTrait;
use MongoDB\Laravel\Eloquent\Model;

class ProductVariation extends Model
{
    use ProductVariationRelationsTrait, ProductVariationAttributesTrait;
    protected $connection = 'mongodb';
    protected $collection = 'variations';
    protected $fillable = ['product_id', 'sku', 'price', 'sale_price'];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'product_id' => 'string',
    ];


}