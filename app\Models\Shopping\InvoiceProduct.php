<?php

namespace App\Models\Shopping;

use App\Traits\InvoiceProduct\InvoiceProductRelationsTrait;
use App\Traits\InvoiceProduct\InvoiceProductAttributesTrait;
use MongoDB\Laravel\Eloquent\Model;

/**
 * InvoiceProduct Model
 *
 * Represents a product in an invoice with details and quantity.
 *
 * @property string $_id The unique identifier for the invoice product
 * @property string $invoice_id The ID of the invoice this product belongs to
 * @property string $product_id The ID of the product
 * @property string $variant_id The ID of the product variation
 * @property string $name The name/title of the product (snapshot)
 * @property float $price The regular price of the product variation (snapshot)
 * @property float|null $sale_price The sale price of the product variation (snapshot)
 * @property int $quantity The quantity of this product in the invoice
 * @property string|null $image The image URL for this product (snapshot)
 * @property-read float $total The calculated total price (considering sale price if available)
 * @property-read Invoice $invoice The invoice this product belongs to
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\InvoiceProductDetail[] $details The details of this invoice product
 */
class InvoiceProduct extends Model
{
    use InvoiceProductRelationsTrait, InvoiceProductAttributesTrait;
    protected $connection = 'mongodb';
    protected $collection = 'invoice_products';

    protected $fillable = [
        'invoice_id',
        'product_id',
        'variant_id',
        'name',
        'price',
        'sale_price',
        'quantity',
        'image',
    ];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'quantity' => 'integer',
        'invoice_id' => 'string',
        'product_id' => 'string',
        'variant_id' => 'string',
    ];



}
