<?php

namespace App\Models\Shopping;

use App\Traits\InvoiceProductDetail\InvoiceProductDetailRelationsTrait;
use MongoDB\Laravel\Eloquent\Model;

/**
 * InvoiceProductDetail Model
 *
 * Represents a detail of a product in an invoice.
 *
 * @property string $_id The unique identifier for the invoice product detail
 * @property string $invoice_product_id The ID of the invoice product this detail belongs to
 * @property string $key The key of the detail (e.g., 'color', 'size')
 * @property string $value The value of the detail (e.g., 'red', 'large')
 * @property-read InvoiceProduct $invoiceProduct The invoice product this detail belongs to
 */
class InvoiceProductDetail extends Model
{
    use InvoiceProductDetailRelationsTrait;
    protected $connection = 'mongodb';
    protected $collection = 'invoice_product_details';

    protected $fillable = [
        'invoice_product_id',
        'key',
        'value',
    ];

    protected $casts = [
        'invoice_product_id' => 'string',
    ];


}
