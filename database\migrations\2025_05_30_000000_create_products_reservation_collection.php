<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

/**
 * Migration for creating the products_reservation collection in MongoDB.
 * This collection stores temporary reservations of product variants during checkout process.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('products_reservations', function (Blueprint $collection) {
            $collection->string('user_id');
            $collection->string('invoice_id');
            $collection->string('variant_id');
            $collection->integer('quantity');
            $collection->timestamp('expire_date');
            $collection->string('status');
            $collection->text('note')->nullable();
            $collection->timestamp('restored_at')->nullable();
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('products_reservations');
    }
};
