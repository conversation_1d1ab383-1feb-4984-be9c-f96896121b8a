<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use MongoDB\Laravel\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('product_details', function (Blueprint $collection) {
            $collection->index('product_id');
            $collection->string('key');
            $collection->string('value');
            $collection->string('type');
            $collection->string('target');
            $collection->string('status');
            $collection->timestamps();

            $collection->unique(['product_id', 'key']);
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('product_details');
    }
};
