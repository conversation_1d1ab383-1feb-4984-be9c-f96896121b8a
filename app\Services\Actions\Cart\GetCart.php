<?php

namespace App\Services\Actions\Cart;

use App\Models\Shopping\ShoppingCart;

/**
 * GetCart Action
 *
 * Retrieves the current user's shopping cart with all items.
 * If the cart doesn't exist, it creates an empty one.
 */
class GetCart
{
    /**
     * Handle retrieving the user's cart.
     *
     * @param array $data Empty array as we don't need any data from the request
     * @return \App\Models\Shopping\ShoppingCart
     */
    public function handle(array $data): ShoppingCart
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();

        // Find the user's cart
        $cart = ShoppingCart::with(
            'items.productVariation.product',
            'items.productVariation.attributes'
        )
            ->where('user_id', $userId)
            ->first();
        // If no cart exists, create an empty one
        if (!$cart) {
            $cart = ShoppingCart::create(['user_id' => $userId]);
            $cart->load(
                'items.productVariation.product',
                'items.productVariation.attributes'
            );

        }

        return $cart;
    }
}
