<?php

namespace App\Services\Actions\Address;


/**
 * DeleteAddress Action
 *
 * Deletes an existing address for a user.
 */
class DeleteAddress
{
    /**
     * <PERSON>le deleting an existing address.
     *
     * @param array $data [
     *   'address_id' => string,
     *   'address_model' => Address - The address model from the Form Request
     * ]
     * @return bool
     */
    public function handle(array $data): bool
    {
        // Get the address model from the Form Request
        $address = $data['address_model'];

        // Delete the address
        return $address->delete();
    }
}
