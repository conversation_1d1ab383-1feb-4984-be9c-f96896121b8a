<?php

namespace App\Http\Resources\Content;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Article models into API responses.
 *
 * Provides a representation of an article with its title, content, and related information.
 */
class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Article ID
     * - Title
     * - Content (HTML)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => (string) $this->_id,
            'title' => $this->title,
            'content' => $this->content,
        ];



        return $data;
    }
}
