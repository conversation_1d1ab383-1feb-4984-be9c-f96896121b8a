<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Transaction models into API responses.
 *
 * Provides a representation of a transaction with its status, amount, and payment details.
 */
class TransactionResource extends JsonResource
{
    /**
     * The payment gateway URL for the transaction.
     *
     * @var string|null
     */
    protected ?string $gatewayUrl = null;

    /**
     * Create a new resource instance.
     *
     * @param mixed $resource The transaction model
     * @param string|null $gatewayUrl The payment gateway URL
     * @return void
     */
    public function __construct($resource, ?string $gatewayUrl = null)
    {
        parent::__construct($resource);
        $this->gatewayUrl = $gatewayUrl;
    }

    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Payment link
     * - Status (pending, rejected, paid)
     * - Amount
     * - Payment method
     * - Payment gateway
     * - Track code
     * - Description
     * - Payment date
     * - Creation date
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'user_id' => $this->user_id,
            'status' => $this->status,
            'amount' => $this->amount,
            'payment_method' => $this->payment_method,
            'payment_gateway' => $this->payment_gateway,
            'track_code' => $this->track_code,
            'description' => $this->description,
            'paid_at' => $this->shamsiPaidAt,
            'created_at' => $this->shamsiCreatedAt,
        ];

        // Add payment link if available
        if ($this->gatewayUrl) {
            $data['payment_link'] = $this->gatewayUrl;
        }

        return $data;
    }


}
