<?php

use Illuminate\Database\Migrations\Migration;
use MongoDB\Laravel\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the invoice_products collection in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('invoice_products', function (Blueprint $collection) {
            $collection->string('invoice_id'); // FK to invoices._id
            $collection->string('product_id'); // Reference to the product
            $collection->string('variant_id'); // Reference to the product variation

            $collection->integer('quantity'); // Quantity of this product in the invoice
            $collection->double('price'); // Regular price of the product at the time of purchase
            $collection->double('sale_price')->nullable(); // Sale price of the product at the time of purchase (if applicable)
            $collection->string('name'); // Snapshot of the product name
            $collection->string('image')->nullable(); // Snapshot of the product image URL

            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('invoice_products');
    }
};
