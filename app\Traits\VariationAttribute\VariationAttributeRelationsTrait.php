<?php

namespace App\Traits\VariationAttribute;

use App\Models\Product\ProductVariation;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * VariationAttribute Relations Trait
 *
 * This trait contains all relationship methods for the VariationAttribute model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\VariationAttribute
 */
trait VariationAttributeRelationsTrait
{
    /**
     * Get the product variation that this attribute belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function variation(): BelongsTo
    {
        return $this->belongsTo(ProductVariation::class);
    }
}
