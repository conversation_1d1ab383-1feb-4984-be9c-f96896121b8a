<?php

namespace App\Traits\DeliveryMethod;

use App\Models\User\Shop;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * DeliveryMethod Relations Trait
 *
 * This trait contains all relationship methods for the DeliveryMethod model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\DeliveryMethod
 */
trait DeliveryMethodRelationsTrait
{
    /**
     * Get the shop that this delivery method belongs to.
     * If shop_id is null, this is a global delivery method available to all shops.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }
}
