<?php

namespace App\Console\Commands;

use App\Models\Product\ProductsReservation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RejectExpiredReservations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reject-expired-reservations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rejects reservations that have passed their expiration date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to reject expired reservations...');
        Log::channel('scheduled')->info('Starting RejectExpiredReservations command');

        try {
            // Current time to compare with expiration dates
            $now = now();

            DB::transaction(function () use ($now) {
                // Find pending reservations that have expired
                $expiredReservations = ProductsReservation::where('status', 'pending')
                    ->where('expire_date', '<', $now)
                    ->get();

                $count = $expiredReservations->count();
                Log::channel('scheduled')->info("Found {$count} expired reservations to reject");
                $this->info("Found {$count} expired reservations to reject");

                foreach ($expiredReservations as $reservation) {
                    // Update reservation status to rejected
                    $reservation->update([
                        'status' => 'rejected',
                        'restored_at' => $now,
                        'note' => 'رزرو به دلیل انقضای زمان رد شد'
                    ]);

                    Log::channel('scheduled')->info("Rejected reservation ID: {$reservation->_id}, expired at: {$reservation->expire_date}");
                }
            });

            $this->info('Successfully rejected expired reservations');
            Log::channel('scheduled')->info('Completed RejectExpiredReservations command');

            return 0; // Success
        } catch (\Exception $e) {
            $this->error('Error rejecting expired reservations: ' . $e->getMessage());
            Log::channel('scheduled')->error('Error in RejectExpiredReservations command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1; // Error
        }
    }
}
