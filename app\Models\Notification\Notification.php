<?php

namespace App\Models\Notification;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;

/**
 * Notification Model
 *
 * Represents a notification that can be sent to users through various channels.
 * Supports scheduling, error tracking, and payload data for Firebase Cloud Messaging.
 *
 * @property string $_id MongoDB document ID
 * @property string $title The notification title
 * @property string $description The notification body text
 * @property string|null $image Optional URL to an image to display with the notification
 * @property array $payload Additional data to send with the notification
 * @property \Carbon\Carbon $time_to_send When the notification should be sent
 * @property string $type The notification type ('token' or 'topic')
 * @property string $target The target token or topic
 * @property string $status The notification status ('pending', 'sent', 'failed')
 * @property \Carbon\Carbon|null $sent_time When the notification was actually sent
 * @property string|null $error Error details if the notification failed to send
 * @property \Carbon\Carbon $created_at When the notification was created
 * @property \Carbon\Carbon $updated_at When the notification was last updated
 */
class Notification extends Model
{
    use HasFactory;

    /**
     * The database connection used by the model.
     *
     * @var string
     */
    protected $connection = 'mongodb';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'title',
        'description',
        'image',
        'payload',
        'time_to_send',
        'type',
        'target',
        'status',
        'sent_time',
        'error',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'time_to_send' => 'datetime',
        'sent_time' => 'datetime',
        'payload' => 'array',
    ];
}
