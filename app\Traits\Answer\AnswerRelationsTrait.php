<?php

namespace App\Traits\Answer;

use App\Models\UserInteraction\Question;
use App\Models\User\User;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * Answer Relations Trait
 *
 * This trait contains all relationship methods for the Answer model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Answer
 */
trait AnswerRelationsTrait
{
    /**
     * Get the question that this answer belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    /**
     * Get the user that created this answer.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
