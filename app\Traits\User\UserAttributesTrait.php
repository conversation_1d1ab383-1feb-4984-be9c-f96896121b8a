<?php

namespace App\Traits\User;

use App\Models\User\WalletTransaction;

/**
 * User Attributes Trait
 *
 * This trait contains all attribute methods for the User model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\User
 */
trait UserAttributesTrait
{
    /**
     * Check if the user has a seller role.
     *
     * @return bool
     */
    public function isSeller(): bool
    {
        return $this->role === 'seller';
    }

    /**
     * Check if the user has a buyer role.
     *
     * @return bool
     */
    public function isBuyer(): bool
    {
        return $this->role === 'buyer';
    }

    /**
     * Calculate the user's wallet balance.
     *
     * @return float
     */
    public function getWalletBalanceAttribute(): float
    {
        $deposits = $this->walletTransactions()
            ->where('type', WalletTransaction::TYPE_DEPOSIT)
            ->sum('amount');

        $withdrawals = $this->walletTransactions()
            ->where('type', WalletTransaction::TYPE_WITHDRAW)
            ->sum('amount');

        return $deposits - $withdrawals;
    }
}
