<?php

namespace App\Traits\Invoice;

/**
 * Invoice Attributes Trait
 *
 * This trait contains all attribute methods for the Invoice model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Invoice
 */
trait InvoiceAttributesTrait
{
    /**
     * Calculate the total price of all products in this invoice.
     *
     * @return float
     */
    public function getTotalAttribute()
    {
        // Sum the total for all products (using effective price)
        return $this->products->sum(function ($product) {
            return $product->total;
        });
    }

    /**
     * Calculate the total discount amount for all products in this invoice.
     *
     * @return float
     */
    public function getTotalDiscountAttribute()
    {
        // Sum the discount for all products
        return $this->products->sum(function ($product) {
            return $product->discount;
        });
    }
}
