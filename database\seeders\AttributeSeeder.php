<?php

namespace Database\Seeders;

use App\Models\Product\Attribute;
use Illuminate\Database\Seeder;

/**
 * Seeder for creating initial product attributes.
 * Creates common attribute types (color, size, weight) and their values.
 */
class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating product attributes...');

        // Create color attribute with values
        $colorAttribute = Attribute::create([
            'parent_id' => null,
            'title' => 'رنگ',
            'value' => null,
        ]);

        // Create color values
        $colors = [
            ['title' => 'قرمز', 'value' => '#ff0000'],
            ['title' => 'آبی', 'value' => '#0000ff'],
            ['title' => 'سبز', 'value' => '#00ff00'],
            ['title' => 'سیاه', 'value' => '#000000'],
            ['title' => 'سفید', 'value' => '#ffffff'],
        ];

        foreach ($colors as $color) {
            Attribute::create([
                'parent_id' => (string) $colorAttribute->_id,
                'title' => $color['title'],
                'value' => $color['value'],
            ]);
        }

        // Create size attribute with values
        $sizeAttribute = Attribute::create([
            'parent_id' => null,
            'title' => 'سایز',
            'value' => null,
        ]);

        // Create size values
        $sizes = [
            ['title' => 'X', 'value' => 'small'],
            ['title' => 'XL', 'value' => 'large'],
            ['title' => 'XXL', 'value' => 'extra-large'],
        ];

        foreach ($sizes as $size) {
            Attribute::create([
                'parent_id' => (string) $sizeAttribute->_id,
                'title' => $size['title'],
                'value' => $size['value'],
            ]);
        }

        // Create weight attribute with values
        $weightAttribute = Attribute::create([
            'parent_id' => null,
            'title' => 'وزن',
            'value' => null,
        ]);

        // Create weight values
        $weights = [
            ['title' => 'سبک', 'value' => 'light'],
            ['title' => 'متوسط', 'value' => 'medium'],
            ['title' => 'سنگین', 'value' => 'heavy'],
        ];

        foreach ($weights as $weight) {
            Attribute::create([
                'parent_id' => (string) $weightAttribute->_id,
                'title' => $weight['title'],
                'value' => $weight['value'],
            ]);
        }

        $this->command->info('Product attributes created successfully.');
    }
}
