<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User\User;
use App\Models\User\Address;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

/**
 * Seeder for creating sample addresses in the database.
 */
class AddressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates sample addresses for each user in the database.
     * Each user gets 1-3 addresses with Persian location data.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();

        // Iranian provinces and cities
        $locations = [
            'تهران' => ['تهران', 'شهریار', 'اسلامشهر', 'دماوند'],
            'اصفهان' => ['اصفهان', 'کاشان', 'نجف‌آباد', 'شاهین‌شهر'],
            'خراسان رضوی' => ['مشهد', 'نیشابور', 'سبزوار', 'تربت حیدریه'],
            'فارس' => ['شیراز', 'مرودشت', 'جهرم', 'فسا'],
            'آذربایجان شرقی' => ['تبریز', 'مراغه', 'میانه', 'اهر'],
            'خوزستان' => ['اهواز', 'دزفول', 'آبادان', 'بندر ماهشهر'],
            'مازندران' => ['ساری', 'بابل', 'آمل', 'قائم‌شهر'],
            'گیلان' => ['رشت', 'انزلی', 'لاهیجان', 'رودسر'],
        ];

        // Address name options
        $addressNames = ['خانه', 'محل کار', 'خانه پدری', 'خانه دوم', 'دفتر', 'فروشگاه'];

        foreach ($users as $user) {
            // Create 1-3 addresses for each user
            $numAddresses = rand(1, 3);

            for ($i = 0; $i < $numAddresses; $i++) {
                // Select a random province
                $province = array_rand($locations);

                // Select a random city from the province
                $cities = $locations[$province];
                $city = $cities[array_rand($cities)];

                // Generate a random address name
                $name = $addressNames[array_rand($addressNames)];

                // Generate a random is_recipient_self value (mostly true)
                $isRecipientSelf = (rand(0, 5) > 0); // 5/6 chance of being true

                // Generate receiver name and phone based on is_recipient_self
                $receiverName = $isRecipientSelf ? $user->full_name : Faker::firstName() . ' ' . Faker::lastName();
                $receiverPhone = $isRecipientSelf ? $user->phone : '09' . rand(10, 99) . rand(1000000, 9999999); // Format: 09XXXXXXXXX

                // Create the address
                Address::create([
                    'user_id' => $user->_id,
                    'name' => $name,
                    'receiver_name' => $receiverName,
                    'receiver_phone' => $receiverPhone,
                    'is_recipient_self' => $isRecipientSelf,
                    'province' => $province,
                    'city' => $city,
                    'zip_code' => rand(1000000000, 9999999999), // 10-digit zip code
                    'address' => Faker::address(),
                    'latitude' => rand(25, 40) + rand(0, 1000000) / 1000000, // Random latitude in Iran's range
                    'longitude' => rand(44, 63) + rand(0, 1000000) / 1000000, // Random longitude in Iran's range
                ]);
            }
        }
    }
}
