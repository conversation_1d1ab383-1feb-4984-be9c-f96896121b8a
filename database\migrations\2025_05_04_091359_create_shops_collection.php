<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateShopsCollection extends Migration
{
    public function up(): void
    {
        Schema::connection('mongodb')->create('shops', function (Blueprint $collection) {
            $collection->string('title');
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('shops');
    }
}
