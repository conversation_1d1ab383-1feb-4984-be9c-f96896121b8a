<?php

namespace App\Traits\Attribute;

use App\Models\Product\Attribute;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\HasMany;

/**
 * Attribute Relations Trait
 *
 * This trait contains all relationship methods for the Attribute model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Attribute
 */
trait AttributeRelationsTrait
{
    /**
     * Get the parent attribute (for attribute values).
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Attribute::class, 'parent_id', '_id');
    }

    /**
     * Get the child attributes (values for this attribute title).
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function values(): HasMany
    {
        return $this->hasMany(Attribute::class, 'parent_id', '_id');
    }
}
