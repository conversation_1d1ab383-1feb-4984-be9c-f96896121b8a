<?php

namespace App\Traits\InvoiceProductDetail;

use App\Models\Shopping\InvoiceProduct;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * InvoiceProductDetail Relations Trait
 *
 * This trait contains all relationship methods for the InvoiceProductDetail model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\InvoiceProductDetail
 */
trait InvoiceProductDetailRelationsTrait
{
    /**
     * Get the invoice product that this detail belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function invoiceProduct(): BelongsTo
    {
        return $this->belongsTo(InvoiceProduct::class, 'invoice_product_id');
    }
}
