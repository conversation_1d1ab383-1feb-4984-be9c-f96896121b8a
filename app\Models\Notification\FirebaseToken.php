<?php

namespace App\Models\Notification;

use MongoDB\Laravel\Eloquent\Model;

/**
 * FirebaseToken Model
 *
 * Represents a Firebase Cloud Messaging (FCM) token for a client device.
 * Used to send push notifications to specific devices.
 *
 * @property string $_id MongoDB document ID
 * @property string $agent The user agent of the client device
 * @property string $ip The IP address of the client device
 * @property string $path The path where the token was registered
 * @property string $token The Firebase Cloud Messaging token
 * @property \Carbon\Carbon $created_at When the token was created
 * @property \Carbon\Carbon $updated_at When the token was last updated
 */
class FirebaseToken extends Model
{
    /**
     * The database connection used by the model.
     *
     * @var string
     */
    protected $connection = 'mongodb';

    /**
     * The collection associated with the model.
     *
     * @var string
     */
    protected $collection = 'firebase_tokens';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'agent',
        'ip',
        'path',
        'token',
    ];
}