<?php

namespace App\Traits\Guarantee;

use App\Models\Product\Product;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * Guarantee Relations Trait
 *
 * This trait contains all relationship methods for the Guarantee model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Guarantee
 */
trait GuaranteeRelationsTrait
{
    /**
     * Get the product that this guarantee belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
