<?php

namespace App\Models\Product;

use App\Traits\ProductDetail\ProductDetailRelationsTrait;
use MongoDB\Laravel\Eloquent\Model;

class ProductDetail extends Model
{
    use ProductDetailRelationsTrait;
    protected $connection = 'mongodb';
    protected $collection = 'product_details';

    protected $fillable = [
        'product_id',
        'key',
        'value',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'product_id' => 'string',
    ];


}
