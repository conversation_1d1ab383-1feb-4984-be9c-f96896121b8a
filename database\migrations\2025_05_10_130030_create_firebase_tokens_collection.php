<?php

use Illuminate\Support\Facades\Schema;
use Mongodb\Laravel\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('firebase_tokens', function (Blueprint $collection) {
            $collection->index('firebase_token');
            $collection->string('agent')->nullable();
            $collection->string('ip')->nullable();
            $collection->string('path')->nullable();
            $collection->string('token')->unique();

            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('firebase_tokens');
    }
};