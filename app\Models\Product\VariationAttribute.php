<?php


namespace App\Models\Product;

use App\Traits\VariationAttribute\VariationAttributeRelationsTrait;
use MongoDB\Laravel\Eloquent\Model;

class VariationAttribute extends Model
{
    use VariationAttributeRelationsTrait;
    protected $connection = 'mongodb';
    protected $collection = 'variation_attributes';
    protected $fillable = ['variation_id', 'attribute_title', 'attribute_value', 'attribute_type', 'extra_data'];
    protected $casts = [
        'extra_data' => 'array',
    ];


}
