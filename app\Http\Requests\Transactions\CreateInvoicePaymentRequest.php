<?php

namespace App\Http\Requests\Transactions;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Shopping\Invoice;

/**
 * Create Invoice Payment Request
 *
 * Validates the request data for creating a new invoice payment transaction.
 */
class CreateInvoicePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "invoice_id" => [
                'required',
                'string',
                'exists:invoices,_id',
                function ($_, $value, $fail) {
                    $invoice = Invoice::find($value);

                    if ($invoice && $invoice->user_id !== auth()->id()) {
                        $fail(__('messages.transaction.invoice_not_owned'));
                    }

                    if ($invoice && $invoice->status !== 'pending') {
                        $fail(__('messages.transaction.invoice_already_paid'));
                    }

                    // If payment method is wallet, check if user has any balance
                    if (request('payment_method') === 'wallet') {
                        $user = auth()->user();
                        if ($user->wallet_balance <= 0) {
                            $fail(__('messages.wallet.zero_balance'));
                        }
                    }
                },
            ],
            "payment_method" => [
                'required',
                'string',
                'in:online,wallet',
            ],
        ];
    }
}
