<?php

namespace App\Traits\Question;

use App\Models\UserInteraction\Answer;
use App\Models\User\User;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\HasMany;
use MongoDB\Laravel\Relations\MorphTo;

/**
 * Question Relations Trait
 *
 * This trait contains all relationship methods for the Question model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Question
 */
trait QuestionRelationsTrait
{
    /**
     * Get the parent model that this question belongs to.
     * This can be any model that uses the morphMany relationship with questions.
     *
     * @return \MongoDB\Laravel\Relations\MorphTo
     */
    public function questionable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the answers associated with this question.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function answers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Answer::class);
    }

    /**
     * Get the user that created this question.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function user(): <PERSON>ongsTo
    {
        return $this->belongsTo(User::class);
    }
}
