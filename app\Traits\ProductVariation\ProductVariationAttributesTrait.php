<?php

namespace App\Traits\ProductVariation;

/**
 * ProductVariation Attributes Trait
 *
 * This trait contains all attribute methods for the ProductVariation model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\ProductVariation
 */
trait ProductVariationAttributesTrait
{
    /**
     * Get the current quantity of this variation by summing all purchase entries
     * and subtracting active reservations.
     * Positive values are purchases, negative values are sales.
     *
     * @return int
     */
    public function getCurrentQuantityAttribute()
    {
        // Calculate total stock from purchase entries
        $totalStock = $this->purchases->sum('quantity');

        // Get active (non-expired) reservations
        $activeReservations = $this->reservations()
            ->where('status', 'pending')
            ->where('expire_date', '>', now())
            ->sum('quantity');

        // Subtract active reservations from total stock
        return $totalStock - $activeReservations;
    }

    /**
     * Get the quantity of this variation that is currently reserved.
     * Only counts active (non-expired) reservations with 'pending' status.
     *
     * @return int
     */
    public function getReservedQuantityAttribute()
    {
        return $this->reservations()
            ->where('status', 'pending')
            ->where('expire_date', '>', now())
            ->sum('quantity');
    }
}
