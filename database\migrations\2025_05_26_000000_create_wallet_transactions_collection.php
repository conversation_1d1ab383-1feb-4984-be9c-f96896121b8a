<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the wallet_transactions collection in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('wallet_transactions', function (Blueprint $collection) {
            $collection->string('user_id'); // FK to users._id
            $collection->string('type'); // deposit, withdraw
            $collection->double('amount'); // Transaction amount
            $collection->string('referenceable_id')->nullable(); // ID of the related entity (invoice, transaction, etc.)
            $collection->string('referenceable_type')->nullable(); // Type of the related entity (Invoice, Transaction, etc.)
            $collection->string('description')->nullable(); // Description of the transaction
            $collection->json('meta')->nullable(); // Additional metadata
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('wallet_transactions');
    }
};
