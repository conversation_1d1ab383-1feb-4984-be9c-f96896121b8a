<?php

namespace App\Traits\ShoppingCart;

use App\Models\Shopping\CartItem;
use MongoDB\Laravel\Relations\HasMany;

/**
 * ShoppingCart Relations Trait
 *
 * This trait contains all relationship methods for the ShoppingCart model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ShoppingCart
 */
trait ShoppingCartRelationsTrait
{
    /**
     * Get the items in this shopping cart.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class, 'cart_id', '_id');
    }
}
