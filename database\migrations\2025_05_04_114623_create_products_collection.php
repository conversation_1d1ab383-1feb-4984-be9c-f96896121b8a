<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('products', function (Blueprint $collection) {
            $collection->string('title');
            $collection->text('description')->nullable();
            $collection->string('slug')->unique();
            $collection->string('meta_title')->nullable();
            $collection->string('meta_description')->nullable();
            $collection->string('shop_id')->nullable();
            $collection->index('shop_id');
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('products');
    }
};
