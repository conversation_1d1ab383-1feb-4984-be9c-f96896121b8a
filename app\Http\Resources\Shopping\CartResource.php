<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;
use App\Http\Resources\Shopping\CartItemResource;

/**
 * Cart Resource
 */
class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $items = $this->whenLoaded('items');

        // Default values for calculations
        $subtotal = 0;
        $totalDiscount = 0;
        $total = 0;

        // Only calculate if items are loaded
        if (!($items instanceof \Illuminate\Http\Resources\MissingValue)) {
            $subtotal = $items->sum(fn($item) => $item->price * $item->quantity);
            $totalDiscount = $items->sum(fn($item) => $item->discount);
            $total = $items->sum(fn($item) => $item->total);
        }

        return [
            // User ID of the cart owner
            'user_id' => $this->user_id,

            // Transform each cart item using CartItemResource
            'items' => CartItemResource::collection($items),

            // Cart pricing information
            'subtotal' => $subtotal,
            'total_discount' => $totalDiscount,
            'total' => $total,
        ];
    }
}

