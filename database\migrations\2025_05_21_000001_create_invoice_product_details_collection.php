<?php

use Illuminate\Database\Migrations\Migration;
use MongoDB\Laravel\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the invoice_product_details collection in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('invoice_product_details', function (Blueprint $collection) {
            $collection->string('invoice_product_id'); // FK to invoice_products._id
            $collection->string('key'); // Detail key (e.g., 'color', 'size')
            $collection->string('value'); // Detail value (e.g., 'red', 'large')
            
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('invoice_product_details');
    }
};
