<?php

namespace App\Services\Notifications\Drivers;

use App\Services\Notifications\Contracts\NotificationDriverInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Google\Auth\OAuth2;

/**
 * Firebase Cloud Messaging notification driver.
 *
 * This class implements the NotificationDriverInterface to send notifications
 * through Firebase Cloud Messaging (FCM). It supports sending to both
 * individual tokens and topics, and handles authentication with Google's OAuth2.
 */
class FirebaseNotificationDriver implements NotificationDriverInterface
{
    /**
     * The Firebase project ID.
     *
     * @var string
     */
    protected string $projectId;

    /**
     * Stores the last error message if an operation fails.
     *
     * @var string|null
     */
    protected $lastError = null;

    /**
     * Create a new Firebase notification driver instance.
     *
     * @throws \Exception If the Firebase project ID is not configured
     */
    public function __construct()
    {
        $this->projectId = config('firebase.project_id') ?? throw new \Exception('Firebase project ID is required');
    }

    /**
     * Get an OAuth2 access token for Firebase Cloud Messaging.
     *
     * @return string The access token
     * @throws \Exception If Firebase credentials are not configured
     */
    protected function getAccessToken(): string
    {
        $clientEmail = config('firebase.client_email') ?? throw new \Exception('Firebase client email is required');
        $privateKey = config('firebase.private_key') ?? throw new \Exception('Firebase private key is required');

        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $clientEmail,
            'signingAlgorithm' => 'RS256',
            'signingKey' => $privateKey,
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => ['https://www.googleapis.com/auth/firebase.messaging'],
        ]);

        return $oauth->fetchAuthToken()['access_token'];
    }

    /**
     * Send a notification to a specific device token.
     *
     * @param array $data Notification data including:
     *                    - target: The device token
     *                    - title: The notification title
     *                    - description: The notification body
     *                    - image: Optional image URL
     *                    - payload: Optional data payload
     *                    - notification_id: Optional ID to update with error info
     * @return bool True if the notification was sent successfully
     */
    public function sendToToken(array $data): bool
    {
        $response = Http::withToken($this->getAccessToken())
            ->post("https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send", [
                'message' => [
                    'token' => $data['target'],
                    'notification' => [
                        'title' => $data['title'] ?? '',
                        'body' => $data['description'] ?? '',
                        'image' => $data['image'] ?? null,
                    ],
                    'data' => $data['payload'] ?? [],
                ],
            ]);

        if (!$response->successful()) {
            $errorData = $response->json() ?? ['error' => $response->body()];
            $errorMessage = $errorData['error']['message'] ?? $response->body();
            $this->lastError = $errorMessage;

            Log::channel('notifications')->error('Firebase sendToToken failed', [
                'status' => $response->status(),
                'response' => $errorData,
                'error_message' => $errorMessage,
                'target' => $data['target'],
                'title' => $data['title'] ?? '',
            ]);

            // Store the error in the notification model if this is being called from the command
            if (isset($data['notification_id'])) {
                \App\Models\Notification\Notification::where('_id', $data['notification_id'])
                    ->update(['error' => $errorMessage]);
            }
        }

        return $response->successful();
    }

    /**
     * Send a notification to a topic.
     *
     * @param array $data Notification data including:
     *                    - target: The topic name
     *                    - title: The notification title
     *                    - description: The notification body
     *                    - image: Optional image URL
     *                    - payload: Optional data payload
     *                    - notification_id: Optional ID to update with error info
     * @return bool True if the notification was sent successfully
     */
    public function sendToTopic(array $data): bool
    {
        $response = Http::withToken($this->getAccessToken())
            ->post("https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send", [
                'message' => [
                    'topic' => $data['target'],
                    'notification' => [
                        'title' => $data['title'] ?? '',
                        'body' => $data['description'] ?? '',
                        'image' => $data['image'] ?? null,
                    ],
                    'data' => $data['payload'] ?? [],
                ],
            ]);

        if (!$response->successful()) {
            $errorData = $response->json() ?? ['error' => $response->body()];
            $errorMessage = $errorData['error']['message'] ?? $response->body();
            $this->lastError = $errorMessage;

            Log::channel('notifications')->error('Firebase sendToTopic failed', [
                'status' => $response->status(),
                'response' => $errorData,
                'error_message' => $errorMessage,
                'topic' => $data['target'],
                'title' => $data['title'] ?? '',
            ]);

            // Store the error in the notification model if this is being called from the command
            if (isset($data['notification_id'])) {
                \App\Models\Notification\Notification::where('_id', $data['notification_id'])
                    ->update(['error' => $errorMessage]);
            }
        }

        return $response->successful();
    }

    /**
     * Send a notification using the appropriate method based on type.
     *
     * @param array $data Notification data including a 'type' field ('token' or 'topic')
     * @return bool True if the notification was sent successfully
     */
    public function send(array $data): bool
    {
        if ($data['type'] == 'token') {
            return $this->sendToToken($data);
        }

        if ($data['type'] == 'topic') {
            return $this->sendToTopic($data);
        }

        return false;
    }

    /**
     * Get the last error that occurred during a send operation.
     *
     * @return string|null The error message or null if no error occurred
     */
    public function getLastError()
    {
        return $this->lastError;
    }
}
