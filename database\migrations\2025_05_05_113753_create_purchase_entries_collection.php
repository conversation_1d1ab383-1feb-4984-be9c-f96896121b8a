<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('purchase_entries', function (Blueprint $collection) {
            $collection->index(columns: 'variant_id');
            $collection->integer('quantity'); // Can be positive (purchases) or negative (sales)
            $collection->double('price'); // Purchase price or selling price
            $collection->timestamp('purchased_at')->nullable();
            $collection->string('invoice_id')->nullable(); // Only for sales (negative quantities)
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('purchase_entries');
    }
};
