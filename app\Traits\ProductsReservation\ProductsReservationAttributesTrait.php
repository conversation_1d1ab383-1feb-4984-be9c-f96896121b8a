<?php

namespace App\Traits\ProductsReservation;

/**
 * ProductsReservation Attributes Trait
 *
 * This trait contains all attribute methods for the ProductsReservation model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\ProductsReservation
 */
trait ProductsReservationAttributesTrait
{
    /**
     * Check if the reservation is expired.
     *
     * @return bool
     */
    public function getIsExpiredAttribute(): bool
    {
        return now()->gt($this->expire_date);
    }
}
