<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming InvoiceProduct models into API responses.
 */
class InvoiceProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->_id,
            'variant_id' => $this->variant_id,
            'name' => $this->name,
            'price' => $this->price,
            'sale_price' => $this->when($this->sale_price && $this->sale_price < $this->price, $this->sale_price),
            'discount' => $this->when($this->discount > 0, $this->discount),
            'quantity' => $this->quantity,
            'total' => $this->total,
            'image' => $this->image,
            // If details are loaded, include them
            'details' => $this->whenLoaded('details', function() {
                return $this->details->map(function($detail) {
                    return [
                        'key' => $detail->key,
                        'value' => $detail->value,
                    ];
                });
            }, []),
        ];
    }
}
