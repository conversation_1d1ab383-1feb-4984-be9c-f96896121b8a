<?php

namespace App\Services\Actions\Wallet;

use App\Exceptions\PaymentPreprocessException;
use App\Models\Shopping\Transaction;
use App\Models\User\WalletTransaction;
use App\Services\Payment\Contracts\PaymentContract;
use Illuminate\Support\Facades\DB;

/**
 * Top Up Wallet Action
 *
 * Handles the process of adding funds to a user's wallet by creating a payment transaction.
 */
class TopUpWallet
{
    /**
     * Handle the wallet top-up process
     *
     * @param array $data The validated data from the request
     * @return array The created transaction model and payment gateway URL
     * @throws \Exception If transaction creation fails
     */
    public function handle(array $data): array
    {
        return DB::transaction(function () use ($data) {
            // Get the authenticated user
            $user = auth()->user();

            // Get the payment driver
            $driver = app(PaymentContract::class);

            // Prepare payment data
            $paymentData = [
                "amount" => $data["amount"],
                "description" => __('messages.wallet.deposit_description'),
            ];

            // Process the payment with the payment gateway
            $payment = $driver->PaymentPreproccess($paymentData);


            // Create the transaction record with reference to the wallet transaction
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'status' => 'pending',
                'amount' => $data['amount'],
                'payment_method' => 'online',
                'payment_gateway' => $driver->getPaymentGateWayName(),
                'authority' => $payment['authority'] ?? null,
                'description' => $paymentData['description'],
            ]);

            // Return the transaction model and payment gateway URL
            return [
                'model' => $transaction,
                'gateway_url' => $driver->getPaymentGatewayUrl($payment['authority'])
            ];
        });
    }
}
