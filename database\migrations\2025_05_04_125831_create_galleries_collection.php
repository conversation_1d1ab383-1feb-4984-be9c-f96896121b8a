<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('galleries', function (Blueprint $collection) {
            $collection->morphs('imageable'); // adds imageable_id + imageable_type
            $collection->string('image_url');
            $collection->string('caption')->nullable(); // optional SEO caption
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('galleries');
    }
};
