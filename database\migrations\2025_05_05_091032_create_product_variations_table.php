<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use MongoDB\Laravel\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('product_variations', function (Blueprint $collection) {
            $collection->index('product_id');
            $collection->string('sku');
            $collection->double('price'); // Using double for large price values
            $collection->double('sale_price')->nullable(); // Sale price for special offers
            $collection->timestamps();
        });

        Schema::connection('mongodb')->create('variation_attributes', function (Blueprint $collection) {
            $collection->index('variation_id');
            $collection->string('attribute_title');
            $collection->string('attribute_value');
            $collection->string('attribute_type'); // Type of attribute (e.g., 'color', 'size')
            $collection->json('extra_data')->nullable(); // flexible custom data . e.g. color code #000
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('variations');
        Schema::connection('mongodb')->dropIfExists('variation_attributes');
    }
};
