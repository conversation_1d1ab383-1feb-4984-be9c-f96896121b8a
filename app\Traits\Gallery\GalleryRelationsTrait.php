<?php

namespace App\Traits\Gallery;

use MongoDB\Laravel\Relations\MorphTo;

/**
 * Gallery Relations Trait
 *
 * This trait contains all relationship methods for the Gallery model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Gallery
 */
trait GalleryRelationsTrait
{
    /**
     * Get the parent model that this gallery image belongs to.
     * This can be any model that uses the morphMany relationship with gallery.
     *
     * @return \MongoDB\Laravel\Relations\MorphTo
     */
    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }
}
