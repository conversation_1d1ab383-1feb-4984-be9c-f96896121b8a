<?php

namespace App\Traits\ProductVariation;

use App\Models\Product\Product;
use App\Models\Product\ProductsReservation;
use App\Models\Product\PurchaseEntry;
use App\Models\Product\VariationAttribute;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\HasMany;

/**
 * ProductVariation Relations Trait
 *
 * This trait contains all relationship methods for the ProductVariation model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ProductVariation
 */
trait ProductVariationRelationsTrait
{
    /**
     * Get the product that this variation belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', '_id');
    }

    /**
     * Get the attributes associated with this variation.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function attributes(): HasMany
    {
        return $this->hasMany(VariationAttribute::class, 'variation_id', '_id');
    }

    /**
     * Get the purchase entries associated with this variation.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function purchases(): HasMany
    {
        return $this->hasMany(PurchaseEntry::class, 'variant_id');
    }

    /**
     * Get the active reservations associated with this variation.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(ProductsReservation::class, 'variant_id', '_id');
    }
}
