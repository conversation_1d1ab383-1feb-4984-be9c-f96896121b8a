<?php

namespace App\Traits\Category;

use App\Models\Product\Category;
use App\Models\Product\Product;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\BelongsToMany;
use MongoDB\Laravel\Relations\HasMany;

/**
 * Category Relations Trait
 *
 * This trait contains all relationship methods for the Category model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Category
 */
trait CategoryRelationsTrait
{
    /**
     * Get the parent category of this category.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id', '_id');
    }

    /**
     * Get the child categories of this category.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id', '_id');
    }

    /**
     * Get the products that belong to this category.
     *
     * @return \MongoDB\Laravel\Relations\BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'category_product', 'category_id', 'product_id');
    }
}
