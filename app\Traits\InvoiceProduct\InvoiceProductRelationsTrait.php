<?php

namespace App\Traits\InvoiceProduct;

use App\Models\Shopping\Invoice;
use App\Models\Shopping\InvoiceProductDetail;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\HasMany;

/**
 * InvoiceProduct Relations Trait
 *
 * This trait contains all relationship methods for the InvoiceProduct model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\InvoiceProduct
 */
trait InvoiceProductRelationsTrait
{
    /**
     * Get the invoice that this product belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the details associated with this invoice product.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function details(): HasMany
    {
        return $this->hasMany(InvoiceProductDetail::class, 'invoice_product_id', '_id');
    }
}
