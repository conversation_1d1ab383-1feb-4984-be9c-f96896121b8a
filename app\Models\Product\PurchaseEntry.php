<?php
namespace App\Models\Product;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class PurchaseEntry extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'purchase_entries';

    protected $fillable = [
        'variant_id',
        'quantity',
        'price',
        'purchased_at',
        'invoice_id',
    ];

    protected $casts = [
        'purchased_at' => 'datetime',
        'invoice_id' => 'string',
        'variant_id' => 'string',
    ];
    public function variant()
    {
        return $this->belongsTo(ProductVariation::class, 'variant_id');
    }

    /**
     * Get the invoice associated with this entry (for sales only).
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Determine if this entry is a purchase (positive quantity) or a sale (negative quantity).
     *
     * @return bool
     */
    public function isPurchase()
    {
        return $this->quantity > 0;
    }

    /**
     * Determine if this entry is a sale (negative quantity).
     *
     * @return bool
     */
    public function isSale()
    {
        return $this->quantity < 0;
    }
}
