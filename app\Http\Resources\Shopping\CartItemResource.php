<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;
use App\Http\Resources\Product\ProductAttributesResource;

/**
 * Cart Item Resource
 */
class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $variant = $this->whenLoaded('productVariation');
        $variant = $variant instanceof \App\Models\Product\ProductVariation ? $variant : null;

        $product = $variant->product;

        return [
            'product_id' => $this->product_id,
            'id' => $this->variant_id,
            'name' => $this->name,
            'sku' => $variant->sku,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'total' => $this->total,
            'image' => $product->gallery()->first()->image_url,
            "attributes" => ProductAttributesResource::collection($variant->attributes),
        ];
    }
}
