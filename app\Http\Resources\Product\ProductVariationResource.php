<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming ProductVariation models into API responses.
 */
class ProductVariationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Basic variation data (id, sku, price, current_quantity)
     * - All attributes directly in the variation object (e.g., color, size)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Start with the basic variation data
        $data = [
            'id' => (string) $this->_id,
            'sku' => $this->sku,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'current_quantity' => $this->current_quantity,
        ];

        // Add attributes directly to the variation
        if ($this->relationLoaded('attributes')) {
            foreach ($this->attributes as $attribute) {
                $attributeType = $attribute->attribute_type;
                $attributeValue = $attribute->attribute_value;

                // For all attributes, just include the value
                $data[$attributeType] = $attributeValue;
            }
        }

        return $data;
    }
}
