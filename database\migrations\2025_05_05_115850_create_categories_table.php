<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Migrations\Migration;


return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('categories', function ($collection) {
            $collection->string('title');
            $collection->string('slug')->unique(); // unique index
            $collection->nullable('parent_id');
            $collection->nullable('type'); // 'product', 'article', etc.
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('categories');
    }
};
