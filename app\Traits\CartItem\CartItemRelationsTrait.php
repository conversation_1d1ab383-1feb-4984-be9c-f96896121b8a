<?php

namespace App\Traits\CartItem;

use App\Models\Product\ProductVariation;
use App\Models\Shopping\ShoppingCart;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * CartItem Relations Trait
 *
 * This trait contains all relationship methods for the CartItem model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\CartItem
 */
trait CartItemRelationsTrait
{
    /**
     * Get the shopping cart that this item belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function cart(): BelongsTo
    {
        return $this->belongsTo(ShoppingCart::class, null, '_id', 'cart_id');
    }

    /**
     * Get the product variation that this item belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */

    public function productVariation(): BelongsTo
    {
        return $this->belongsTo(ProductVariation::class, 'variant_id', '_id');
    }
}
