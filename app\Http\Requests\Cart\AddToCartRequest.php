<?php

namespace App\Http\Requests\Cart;

use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Add To Cart Request
 *
 * Validates the request data for adding items to the shopping cart.
 */
class AddToCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can add items to cart
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'variant_id' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Find the product variant
                    $variant = ProductVariation::find($value);

                    // Validate that the variant exists
                    if (!$variant) {
                        $fail(__('messages.cart.variant_not_found'));
                        return;
                    }

                    // Find the parent product
                    $product = Product::find($variant->product_id);

                    // Validate that the product exists
                    if (!$product) {
                        $fail(__('messages.cart.product_not_found'));
                        return;
                    }
                    $this->merge(
                        [
                            'product'=>$product,
                            'variant'=>$variant,
                        ]
                    );
                    // Store product and variant in request for later use

                },
            ],
            'quantity' => [
                'nullable',
                'integer',
                'min:1',
                function ($attribute, $value, $fail) {
                    // Skip if validation has already failed for variant_id
                    if (!$this->has('variant')) {
                        return;
                    }

                    $variant = $this->variant;
                    $quantity = $value ?? 1;

                    // Check if there's enough stock for the requested quantity
                    if ($variant->getCurrentQuantityAttribute() < $quantity) {
                        $fail(__('messages.cart.insufficient_stock'));
                    }

                    // If item already exists in cart, check total quantity
                    if (auth()->check()) {
                        $cart = auth()->user()->cart;
                        if ($cart) {
                            $existing = $cart->items()
                                ->where('variant_id', (string) $variant->_id)
                                ->first();

                            if ($existing) {
                                $newQty = $existing->quantity + $quantity;

                                // Verify that the new total quantity doesn't exceed available stock
                                if ($variant->getCurrentQuantityAttribute() < $newQty) {
                                    $fail(__('messages.cart.exceeds_available_stock'));
                                }
                            }
                        }
                    }
                },
            ],

        ];
    }


    // No need to prepare data for validation as we'll get the user ID directly in the action
}
