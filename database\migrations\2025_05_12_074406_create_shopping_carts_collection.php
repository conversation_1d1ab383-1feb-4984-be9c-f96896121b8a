<?php

use Illuminate\Database\Migrations\Migration;
use MongoDB\Laravel\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the shopping_carts collection in MongoDB.
 *
 * This migration creates a collection to store shopping cart data for both
 * registered users and guest visitors. Cart items are stored in a separate
 * collection with a reference to the cart ID.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     *
     * Creates the shopping_carts collection with fields for user identification
     * and timestamps for tracking creation and updates.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('shopping_carts', function (Blueprint $collection) {
            $collection->string('user_id')->nullable(); // null for guest carts
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * Removes the shopping_carts collection if it exists.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('shopping_carts');
    }
};
