<?php

namespace App\Models\Product;

use App\Traits\ProductsReservation\ProductsReservationRelationsTrait;
use App\Traits\ProductsReservation\ProductsReservationAttributesTrait;
use MongoDB\Laravel\Eloquent\Model;

/**
 * ProductsReservation Model
 *
 * Represents a temporary reservation of product variants during checkout process.
 *
 * @property string $_id The unique identifier for the reservation
 * @property string $user_id The ID of the user who made the reservation
 * @property string $invoice_id The ID of the invoice associated with this reservation
 * @property string $variant_id The ID of the product variation being reserved
 * @property int $quantity The quantity of the product being reserved
 * @property \Carbon\Carbon $expire_date When the reservation expires
 * @property string $status The status of the reservation
 * @property string|null $note Additional notes about the reservation
 * @property \Carbon\Carbon|null $restored_at When the reservation was restored (if applicable)
 * @property \Carbon\Carbon $created_at When the reservation was created
 * @property \Carbon\Carbon $updated_at When the reservation was last updated
 */
class ProductsReservation extends Model
{
    use ProductsReservationRelationsTrait, ProductsReservationAttributesTrait;
    
    protected $connection = 'mongodb';
    protected $collection = 'products_reservations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'invoice_id',
        'variant_id',
        'quantity',
        'expire_date',
        'status',
        'note',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_id' => 'string',
        'invoice_id' => 'string',
        'variant_id' => 'string',
        'quantity' => 'integer',
        'expire_date' => 'datetime',
        'restored_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
