<?php

use Illuminate\Database\Migrations\Migration;
use MongoDB\Laravel\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the cart_items collection in MongoDB.
 *
 * This migration creates a collection to store individual items in shopping carts.
 * Each cart item references a shopping cart and contains product information.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     *
     * Creates the cart_items collection with fields for cart reference,
     * product information, quantity, and timestamps.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('cart_items', function (Blueprint $collection) {
            $collection->string('cart_id'); // FK to shopping_carts._id
            $collection->string('product_id'); // Reference to the product
            $collection->string('variant_id'); // Reference to the product variation

            $collection->integer('quantity'); // Quantity of this item in the cart
            $collection->double('price'); // Snapshot of the product price at the time of adding to cart
            $collection->string('name'); // Snapshot of the product name
            $collection->string('image')->nullable(); // Snapshot of the product image URL

            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * Removes the cart_items collection if it exists.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('cart_items');
    }
};

