<?php

namespace App\Traits\Transaction;

use Carbon\Carbon;
use Hek<PERSON><PERSON>ser\Verta\Verta;

/**
 * Transaction Attributes Trait
 *
 * This trait contains all attribute methods for the Transaction model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Transaction
 */
trait TransactionAttributesTrait
{
    /**
     * Get the formatted paid date in Shamsi calendar.
     *
     * @return string|null
     */
    public function getShamsiPaidAtAttribute(): ?string
    {
        if (!$this->paid_at) {
            return null;
        }

        $verta = new Verta($this->paid_at);
        return $verta->format('Y/m/d H:i:s');
    }

    /**
     * Get the formatted creation date in Shamsi calendar.
     *
     * @return string
     */
    public function getShamsiCreatedAtAttribute(): string
    {
        $verta = new Verta($this->created_at);
        return $verta->format('Y/m/d H:i:s');
    }
    public function getIsWalletPaymentAttribute(): string
    {
        return $this->payment_method == 'wallet';
    }
}
