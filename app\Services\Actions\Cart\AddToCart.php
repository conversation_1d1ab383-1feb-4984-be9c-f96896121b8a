<?php
namespace App\Services\Actions\Cart;

use App\Models\Product\ProductVariation;
use App\Models\Shopping\ShoppingCart;

/**
 * AddToCart Action
 *
 * Handles adding products to a user's shopping cart.
 * This action is responsible for:
 * - Creating or updating cart items
 * - Handling authenticated user carts
 */
class AddToCart
{
    /**
     * Process adding an item to the cart.
     *
     * @param array $data Input data containing:
     *                    - variant_id: (string) The ID of the product variant to add
     *                    - quantity: (int) The quantity to add, defaults to 1
     *                    - product: The product model (added by the Form Request)
     *                    - variant: The variant model (added by the Form Request)
     * @return \App\Models\Shopping\ShoppingCart The updated shopping cart with the new item added
     */
    public function handle(array $data)
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();
        $quantity = $data['quantity'] ?? 1;


        $variant = ProductVariation::find($data['variant_id']);
        $product = $variant->product;

        // Get or create a shopping cart for this user
        $cart = ShoppingCart::firstOrCreate(['user_id' => $userId]);

        // Check if this product variant is already in the cart
        $existing = $cart->items()
            ->where('product_id', (string) $product->_id)
            ->where('variant_id', (string) $variant->_id)
            ->first();

        if ($existing) {
            // If item already exists in cart, update the quantity
            // Stock validation is already done in the Form Request
            $newQty = $existing->quantity + $quantity;

            // Update the quantity and save
            $existing->quantity = $newQty;
            $existing->save();
        } else {
            // If item doesn't exist in cart, create a new cart item
            // We store a snapshot of product data to preserve pricing and details
            $cart->items()->create([
                'product_id' => (string) $product->_id,
                'variant_id' => (string) $variant->_id,
                'name' => $product->title,
                'price' => $variant->price,
                'sale_price' => $variant->sale_price,
                'quantity' => $quantity,
                'image' => $variant->image ?? $product->main_image ?? null,
            ]);
        }
        $cart->refresh();
        $cart->load(
            'items.productVariation.product',
            'items.productVariation.attributes'
        );
        // Return the updated cart
        return $cart;
    }
}
