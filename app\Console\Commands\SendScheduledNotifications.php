<?php


namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Notification\Notification;
use App\Services\Notifications\NotificationSender;
use Illuminate\Support\Facades\Log;

/**
 * Command to send scheduled notifications that are due.
 *
 * This command finds all pending notifications with a time_to_send value
 * in the past or present, and attempts to send them using the configured
 * notification driver. It updates the notification status and records any
 * errors that occur during sending.
 */
class SendScheduledNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:send-due';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send due notifications that are scheduled';

    /**
     * Execute the console command.
     *
     * Finds and processes all pending notifications that are due to be sent.
     * Updates their status to 'sent' or 'failed' based on the result.
     *
     * @return int Command exit code
     */
    public function handle()
    {
        Log::channel('notifications')->info('Starting SendScheduledNotifications command');
        $now = now();

        $pending = Notification::where('status', 'pending')
            ->where('time_to_send', '<=', $now)
            ->get();

        Log::channel('notifications')->info("Found {$pending->count()} pending notifications to process");
        $driver = new NotificationSender();

        foreach ($pending as $notif) {
            Log::channel('notifications')->info("Processing notification ID: {$notif->_id}", [
                'type' => $notif->type,
                'target' => $notif->target,
                'title' => $notif->title,
            ]);

            $data = [
                'type' => $notif->type,
                'target' => $notif->target,
                'title' => $notif->title,
                'description' => $notif->description,
                'image' => $notif->image,
                'payload' => $notif->payload ?? [],
                'notification_id' => $notif->_id, // Pass the notification ID to the driver
            ];

            try {
                Log::channel('notifications')->info("Attempting to send notification ID: {$notif->_id}");
                $success = $driver->send($data);

                if ($success) {
                    Log::channel('notifications')->info("Successfully sent notification ID: {$notif->_id}");
                    $notif->update([
                        'status' => 'sent',
                        'sent_time' => now()
                    ]);
                } else {
                    $errorMessage = $driver->getLastError() ?: 'Notification service returned failure response';
                    Log::channel('notifications')->warning("Failed to send notification ID: {$notif->_id}", [
                        'error' => $errorMessage
                    ]);

                    $notif->update([
                        'status' => 'failed',
                        'sent_time' => now(), // Record when the failure happened
                        'error' => $errorMessage
                    ]);
                }
            } catch (\Throwable $e) {
                Log::channel('notifications')->error("Exception while sending notification ID: {$notif->_id}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $notif->update([
                    'status' => 'failed',
                    'sent_time' => now(), // Record when the failure happened
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::channel('notifications')->info("Completed SendScheduledNotifications command. Processed {$pending->count()} notifications.");
        $this->info("Processed {$pending->count()} notifications.");

        return 0; // Success
    }
}
