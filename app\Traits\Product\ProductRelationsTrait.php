<?php

namespace App\Traits\Product;

use App\Models\Content\Article;
use App\Models\Product\Category;
use App\Models\UserInteraction\Comment;
use App\Models\Shopping\DeliveryMethod;
use App\Models\Content\Gallery;
use App\Models\Content\Guide;
use App\Models\Product\Guarantee;
use App\Models\Content\Keyword;
use App\Models\Product\ProductDetail;
use App\Models\Product\ProductVariation;
use App\Models\UserInteraction\Question;
use App\Models\User\Shop;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\BelongsToMany;
use MongoDB\Laravel\Relations\HasMany;
use MongoDB\Laravel\Relations\MorphMany;

/**
 * Product Relations Trait
 *
 * This trait contains all relationship methods for the Product model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Product
 */
trait ProductRelationsTrait
{
    /**
     * Get the gallery images associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function gallery(): MorphMany
    {
        return $this->morphMany(Gallery::class, 'imageable');
    }

    /**
     * Get the product details associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function details(): HasMany
    {
        return $this->hasMany(ProductDetail::class);
    }

    /**
     * Get the keywords associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function keywords(): MorphMany
    {
        return $this->morphMany(Keyword::class, 'keywordable');
    }

    /**
     * Get the variations associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function variations(): HasMany
    {
        return $this->hasMany(ProductVariation::class);
    }

    /**
     * Get the categories this product belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class);
    }

    /**
     * Get the comments associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get the questions associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function questions(): MorphMany
    {
        return $this->morphMany(Question::class, 'questionable');
    }

    /**
     * Get the delivery methods available for this product.
     *
     * Note: This should be a belongsToMany relationship, which stores delivery_method_ids
     * directly in the products collection.
     * don't use hasMany relationship !!
     *
     * @return \MongoDB\Laravel\Relations\BelongsToMany
     */
    public function deliveryMethods(): BelongsToMany
    {
        return $this->belongsToMany(DeliveryMethod::class, null, 'delivery_method_ids');
    }

    /**
     * Get the guarantees associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function guarantees(): HasMany
    {
        return $this->hasMany(Guarantee::class);
    }

    /**
     * Get the articles associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function articles(): MorphMany
    {
        return $this->morphMany(Article::class, 'articleable');
    }

    /**
     * Get the shop that this product belongs to.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Get the guides associated with this product.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function guides(): MorphMany
    {
        return $this->morphMany(Guide::class, 'guideable');
    }
}
