<?php

namespace App\Traits\ProductsReservation;

use App\Models\Product\ProductVariation;
use App\Models\Shopping\Invoice;
use App\Models\User\User;
use MongoDB\Laravel\Relations\BelongsTo;

/**
 * ProductsReservation Relations Trait
 *
 * This trait contains all relationship methods for the ProductsReservation model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ProductsReservation
 */
trait ProductsReservationRelationsTrait
{
    /**
     * Get the user that made this reservation.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the invoice associated with this reservation.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the product variation that is being reserved.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariation::class, 'variant_id');
    }
}
