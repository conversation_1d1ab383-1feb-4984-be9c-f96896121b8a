<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // database/migrations/xxxx_xx_xx_create_questions_collection.php

        Schema::connection('mongodb')->create('questions', function ($collection) {
            $collection->string('questionable_id');
            $collection->string('questionable_type');
            $collection->string('user_id');
            $collection->string('body');
            $collection->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
