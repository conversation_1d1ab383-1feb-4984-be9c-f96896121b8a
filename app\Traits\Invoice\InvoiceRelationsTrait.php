<?php

namespace App\Traits\Invoice;

use App\Models\Shopping\InvoiceProduct;
use App\Models\Shopping\Transaction;
use App\Models\User\User;
use MongoDB\Laravel\Relations\BelongsTo;
use MongoDB\Laravel\Relations\HasMany;
use MongoDB\Laravel\Relations\MorphMany;

/**
 * Invoice Relations Trait
 *
 * This trait contains all relationship methods for the Invoice model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Invoice
 */
trait InvoiceRelationsTrait
{
    /**
     * Get the user that owns this invoice.
     *
     * @return \MongoDB\Laravel\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the products associated with this invoice.
     *
     * @return \MongoDB\Laravel\Relations\HasMany
     */
    public function products(): HasMany
    {
        return $this->hasMany(InvoiceProduct::class, 'invoice_id', '_id');
    }

    /**
     * Get the transactions associated with this invoice.
     *
     * @return \MongoDB\Laravel\Relations\MorphMany
     */
    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'payable');
    }
}
