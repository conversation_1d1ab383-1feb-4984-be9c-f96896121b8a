<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Product\Product;
use App\Models\Product\ProductVariation;

/**
 * Seeder for creating default product variants for products without any variants.
 *
 * This seeder performs the following operations:
 * 1. Queries all products that do not have any associated product variants
 * 2. For each product without variants, creates a default product variant
 * 3. Optionally creates initial PurchaseEntry records to give variants starting inventory
 * 4. Uses database transactions to ensure data integrity
 * 5. Includes proper error handling and logging
 * 6. Is idempotent (safe to run multiple times)
 */
class ProductVariantSeeder extends Seeder
{
    /**
     * Default price for products without price information (100,000 IRR)
     */
    private const DEFAULT_PRICE = 100000;

    /**
     * Default initial stock quantity for purchase entries
     */
    private const DEFAULT_INITIAL_STOCK = 50;

    /**
     * Run the database seeds.
     *
     * Creates default product variants for products that don't have any variants.
     * Uses database transactions for data integrity and includes comprehensive
     * error handling and logging.
     */
    public function run(): void
    {
        $this->command->info('Starting creation of default product variants...');

        $createdCount = 0;
        $skippedCount = 0;

        try {
            // Use database transaction for data integrity
            DB::connection('mongodb')->transaction(function () use (&$createdCount, &$skippedCount) {
                // Get all products that don't have any variants
                $productsWithoutVariants = $this->getProductsWithoutVariants();

                if ($productsWithoutVariants->isEmpty()) {
                    $this->command->info('No products without variants found');
                    return;
                }

                $this->command->info('Found ' . $productsWithoutVariants->count() . ' products without variants');

                // Process each product
                foreach ($productsWithoutVariants as $product) {
                    try {
                        // Check again if product already has variants (for idempotency)
                        if ($this->productHasVariants($product)) {
                            $this->command->warn('Product "' . $product->title . '" already has variants, skipped');
                            $skippedCount++;
                            continue;
                        }

                        // Create default variant for this product
                        $variant = $this->createDefaultVariant($product);

                        // Create initial purchase entries for inventory
                        $this->createInitialPurchaseEntries($variant);

                        $this->command->info('Default variant created for product "' . $product->title . '"');

                        $createdCount++;

                    } catch (\Exception $e) {
                        $errorMessage = 'Error creating default variant for product "' . $product->title . '": ' . $e->getMessage();

                        $this->command->error($errorMessage);
                        Log::error('ProductVariantSeeder: ' . $errorMessage, [
                            'product_id' => $product->_id,
                            'exception' => $e
                        ]);

                        $skippedCount++;
                    }
                }
            });

        } catch (\Exception $e) {
            $errorMessage = 'Database transaction error: ' . $e->getMessage();

            $this->command->error($errorMessage);
            Log::error('ProductVariantSeeder transaction failed: ' . $errorMessage, [
                'exception' => $e
            ]);

            throw $e;
        }

        // Display summary
        $this->command->info('Created ' . $createdCount . ' default variants, skipped ' . $skippedCount . ' products');

        $this->command->info('Default product variants creation completed successfully');
    }

    /**
     * Get all products that don't have any associated product variants.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getProductsWithoutVariants()
    {
        return Product::whereDoesntHave('variations')->get();
    }

    /**
     * Check if a product has any variants (for idempotency check).
     *
     * @param Product $product
     * @return bool
     */
    private function productHasVariants(Product $product): bool
    {
        return $product->variations()->exists();
    }

    /**
     * Create a default variant for the given product.
     *
     * @param Product $product
     * @return ProductVariation
     */
    private function createDefaultVariant(Product $product): ProductVariation
    {
        $variantData = [
            'product_id' => (string) $product->_id,
            'sku' => $this->generateSku($product),
            'price' => $this->getDefaultPrice(),
            'sale_price' => null, // No sale price for default variants
        ];

        return ProductVariation::create($variantData);
    }

    /**
     * Create initial purchase entries to give the variant starting inventory.
     * Following the pattern from ClothProductDataSeeder and PurchaseEntrySeeder.
     *
     * @param ProductVariation $variant
     * @return void
     */
    private function createInitialPurchaseEntries(ProductVariation $variant): void
    {
        // Create 1-2 purchase entries for initial stock
        $numEntries = rand(1, 2);

        for ($i = 0; $i < $numEntries; $i++) {
            $variant->purchases()->create([
                'quantity' => rand(20, self::DEFAULT_INITIAL_STOCK), // Positive quantity for purchases
                'price' => $variant->price, // Use the variant's price as purchase price
                'purchased_at' => now()->subDays(rand(0, 30)), // Random date within last 30 days
                'invoice_id' => null, // No invoice for purchases (only for sales)
            ]);
        }
    }

    /**
     * Generate a unique SKU for the product variant.
     * Following the pattern from existing seeders.
     *
     * @param Product $product
     * @return string
     */
    private function generateSku(Product $product): string
    {
        // Use product slug or title to generate SKU
        $baseSlug = $product->slug ?? str_replace(' ', '-', $product->title);
        $baseSku = strtoupper($baseSlug . '-DEFAULT');

        // Ensure uniqueness by checking existing SKUs
        $counter = 1;
        $sku = $baseSku;

        while (ProductVariation::where('sku', $sku)->exists()) {
            $sku = $baseSku . '-' . $counter;
            $counter++;
        }

        return $sku;
    }

    /**
     * Get the default price for the product variant.
     * Uses the generateRandomFakeprice helper function for consistency.
     *
     * @return float
     */
    private function getDefaultPrice(): float
    {
        // Use the same helper function as other seeders for consistency
        // generateRandomFakeprice(6) generates prices like 1,000,000 IRR
        return (float) generateRandomFakeprice(6);
    }
}
