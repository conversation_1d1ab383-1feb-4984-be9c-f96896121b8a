<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\Product\Category;

/**
 * Seeder for attaching categories to the cloth product.
 */
class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Attaches specific categories to the cloth product:
     * - mens-wear
     * - summer-collection
     * - accessories
     */
    public function run(): void
    {
        // 1. Get the product by slug
        $product = Product::where('slug', 'cloth')->first();

        if (!$product) {
            $this->command->warn('No product with slug "cloth" found.');
            return;
        }

        // 2. Get three categories from the database
        $categories = Category::whereIn('slug', [
            'mens-wear',
            'summer-collection',
            'accessories'
        ])->get();

        if ($categories->count() < 3) {
            $this->command->info('Make sure to run CategorySeeder first.');
            return;
        }

        // 3. Attach categories to product using <PERSON><PERSON>'s sync method
        $categoryIds = $categories->pluck('_id')->toArray();
        $product->categories()->sync($categories);

        $this->command->info('Categories attached to product with slug "cloth".');
    }
}
