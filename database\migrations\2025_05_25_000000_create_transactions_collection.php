<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the transactions collection in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('transactions', function (Blueprint $collection) {
            $collection->string('user_id'); // ID of the user who owns this transaction
            $collection->string('payable_id'); // ID of the related model (invoice, user, etc.)
            $collection->string('payable_type'); // Type of the related model (Invoice, User, etc.)
            $collection->string('status')->default('pending'); // pending, rejected, paid
            $collection->string('track_code')->nullable(); // Payment tracking code
            $collection->double('amount'); // Transaction amount
            $collection->string('payment_method'); // How payment was made (wallet, online, etc.)
            $collection->string('payment_gateway')->nullable(); // Payment gateway used (stripe, zarinpal, etc.)
            $collection->string('authority')->nullable(); // Payment authority code from payment gateway
            $collection->string('ref_id')->nullable(); // Reference ID from payment gateway
            $collection->string('description')->nullable(); // Description of the transaction
            $collection->timestamp('paid_at')->nullable(); // When the payment was completed

            // Additional transaction details
            $collection->string('source')->nullable(); // Source of the transaction (web, mobile, etc.)
            $collection->string('terminal_id')->nullable(); // Terminal ID for POS transactions
            $collection->string('ip')->nullable(); // IP address of the customer
            $collection->string('card_number')->nullable(); // Masked card number
            $collection->string('card_hash')->nullable(); // Hashed card number
            $collection->double('fee')->nullable(); // Transaction fee
            $collection->string('fee_type')->nullable(); // Type of fee (Merchant, Customer)
            $collection->double('shaparak_fee')->nullable(); // Shaparak network fee
            $collection->string('order_id')->nullable(); // Order ID from payment gateway
            $collection->double('wages')->nullable(); // Wages amount if applicable
            $collection->integer('code')->nullable(); // Response code from payment gateway

            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('transactions');
    }
};
