<?php
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUsersCollection extends Migration
{
    public function up()
    {
        Schema::connection('mongodb')->create('users', function (Blueprint $collection) {
            $collection->string('full_name');
            $collection->string('email');
            $collection->string('phone');
            $collection->string('password');
            $collection->string('role')->default('buyer'); 
            $collection->timestamps();
        });
    }

    public function down()
    {
        Schema::connection('mongodb')->drop('users');
    }
}
