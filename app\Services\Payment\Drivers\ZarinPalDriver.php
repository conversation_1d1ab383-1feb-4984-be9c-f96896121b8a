<?php

namespace App\Services\Payment\Drivers;

use App\Exceptions\PaymentPreprocessException;
use App\Services\Payment\Contracts\PaymentContract;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

/**
 * ZarinPal Payment Driver
 *
 * Handles payment processing through the ZarinPal payment gateway.
 */
class ZarinPalDriver implements PaymentContract
{
    /**
     * Prepares the payment process for Zarinpal gateway.
     *
     * @param array $data Payment data
     * @param float $data['amount'] The amount to be paid
     * @param string $data['description'] Description of the payment
     * @return array Payment preparation result
     * @throws PaymentPreprocessException If payment preprocessing fails
     */
    public function PaymentPreproccess(array $data): array
    {
        // Ensure required data is present
        if (!isset($data['amount']) || !isset($data['description'])) {
            throw new PaymentPreprocessException(
                __('messages.payment.missing_parameters'),
                ['required_fields' => ['amount', 'description']]
            );
        }

        $amount = $data['amount'];
        $description = $data['description'];
        $callbackUrl = config('payment.drivers.zarinpal.callback_url');
        $merchantId = config('payment.drivers.zarinpal.merchant_id');
        $gatewayUrl = config('payment.drivers.zarinpal.gateway_url');
        try {
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post($gatewayUrl, [
                        'merchant_id' => $merchantId,
                        'amount' => $amount,
                        'callback_url' => $callbackUrl,
                        'description' => $description,
                    ]);

            // Access the response data
            $responseData = $response->json();


            // Check if the request failed or the code is not 100
            if (!$response->successful()) {
                $errorMessage = $responseData['errors']['message'] ?? __('messages.payment.gateway_error');
                $errorCode = $response->status();

                throw new PaymentPreprocessException(
                    $errorMessage,
                    ['response' => $responseData, 'status_code' => $errorCode],
                    Response::HTTP_BAD_GATEWAY
                );
            }

            // Check if the response contains the expected success code
            if (($responseData['data']['code'] ?? null) !== 100) {
                $errorMessage = $responseData['errors']['message'] ?? __('messages.payment.invalid_response');

                throw new PaymentPreprocessException(
                    $errorMessage,
                    ['response' => $responseData],
                    Response::HTTP_BAD_GATEWAY
                );
            }

            // Return the successful response data
            return $responseData['data'];

        } catch (\Exception $e) {
            // Catch any other exceptions that might occur during the HTTP request
            if (!($e instanceof PaymentPreprocessException)) {
                throw new PaymentPreprocessException(
                    __('messages.payment.unknown_error'),
                    ['exception' => $e->getMessage()],
                    Response::HTTP_INTERNAL_SERVER_ERROR,
                    $e
                );
            }

            throw $e;
        }

    }

    public function getPaymentGateWayName(): string
    {
        return 'zarinpal';
    }

    public function getPaymentGatewayUrl(string $authority): string
    {
        return "https://sandbox.zarinpal.com/pg/StartPay/$authority";
    }

    public function getGatewayVerificationRules(): array
    {
        return [
            'authority' => [
                'required',
                'string',
                'exists:transactions,authority',
            ],
            'status' => ['required', 'string', 'in:OK'],
        ];
    }

    public function verifyPayment(array $data): array
    {
        $amount = $data['amount'];
        $authority = $data['authority'];
        $merchantId = config('payment.drivers.zarinpal.merchant_id');
        $verifyUrl = config('payment.drivers.zarinpal.verify_url');

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post($verifyUrl, [
                    'merchant_id' => $merchantId,
                    'amount' => $amount,
                    'authority' => $authority,
                ]);

        $responseData = $response->json();

        if (!$response->successful() || (($responseData['data']['code'] ?? null) !== 100 && ($responseData['data']['code'] ?? null) !== 101)) {
            $errorMessage = $responseData['errors']['message'] ?? __('messages.payment.verification_failed');
            throw new PaymentPreprocessException(
                $errorMessage,
                ['response' => $responseData],
                Response::HTTP_BAD_GATEWAY
            );
        }

        return $responseData;
    }
}
