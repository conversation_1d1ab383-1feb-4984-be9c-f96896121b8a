<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the addresses collection in MongoDB.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('addresses', function (Blueprint $collection) {
            $collection->string('user_id');  // FK to users._id
            $collection->string('name');     // Name of the address (e.g., "Home", "Work")
            $collection->string('receiver_name');  // Name of the person receiving packages at this address
            $collection->string('receiver_phone'); // Phone number of the person receiving packages at this address
            $collection->boolean('is_recipient_self')->default(true); // Whether the address is for the user themselves or someone else
            $collection->string('province');
            $collection->string('city');
            $collection->string('zip_code');
            $collection->string('address');  // Detailed address
            $collection->double('latitude')->nullable();
            $collection->double('longitude')->nullable();
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('addresses');
    }
};
