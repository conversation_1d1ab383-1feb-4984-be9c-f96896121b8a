<?php

namespace App\Traits\WalletTransaction;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;

/**
 * WalletTransaction Attributes Trait
 *
 * This trait contains all attribute methods for the WalletTransaction model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\WalletTransaction
 */
trait WalletTransactionAttributesTrait
{
    /**
     * Get the formatted creation date in Shamsi calendar.
     *
     * @return string
     */
    public function getShamsiCreatedAtAttribute(): string
    {
        $verta = new Verta($this->created_at);
        return $verta->format('Y/m/d H:i:s');
    }

    /**
     * Get the formatted update date in Shamsi calendar.
     *
     * @return string
     */
    public function getShamsiUpdatedAtAttribute(): string
    {
        $verta = new Verta($this->updated_at);
        return $verta->format('Y/m/d H:i:s');
    }

    /**
     * Get a human-readable transaction type.
     *
     * @return string
     */
    public function getTypeTextAttribute(): string
    {
        return match($this->type) {
            self::TYPE_DEPOSIT => __('messages.wallet.type_deposit'),
            self::TYPE_WITHDRAW => __('messages.wallet.type_withdraw'),
            default => $this->type,
        };
    }

    /**
     * Determine if this is a deposit transaction.
     *
     * @return bool
     */
    public function getIsDepositAttribute(): bool
    {
        return $this->type === self::TYPE_DEPOSIT;
    }

    /**
     * Determine if this is a withdrawal transaction.
     *
     * @return bool
     */
    public function getIsWithdrawAttribute(): bool
    {
        return $this->type === self::TYPE_WITHDRAW;
    }
}
