<?php


namespace App\Models\Product;

use App\Traits\Category\CategoryRelationsTrait;
use App\Traits\Category\CategoryAttributesTrait;
use MongoDB\Laravel\Eloquent\Model;

class Category extends Model
{
    use CategoryRelationsTrait, CategoryAttributesTrait;
    protected $connection = 'mongodb';
    protected $collection = 'categories';

    protected $fillable = ['title', 'slug', 'parent_id', 'type'];

    protected $casts = [
        'parent_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

}
