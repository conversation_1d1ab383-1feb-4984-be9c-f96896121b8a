<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mongodb')->create('guides', function ($collection) {
            $collection->string('title');
            $collection->text('content'); // for HTML content
            $collection->integer('order')->default(0); // for ordering guides
            $collection->string('guideable_type'); // Morph type
            $collection->string('guideable_id');   // Morph id (use string, not ObjectId)
            $collection->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('guides');
    }
};
