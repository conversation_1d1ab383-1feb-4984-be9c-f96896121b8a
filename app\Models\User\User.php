<?php

namespace App\Models\User;

use App\Traits\User\UserRelationsTrait;
use App\Traits\User\UserAttributesTrait;
use Illuminate\Notifications\Notifiable;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use MongoDB\Laravel\Eloquent\Model;

class User extends Model implements AuthenticatableContract
{
    use Notifiable, Authenticatable, UserRelationsTrait, UserAttributesTrait;

    protected $connection = 'mongodb';


    protected $primaryKey = '_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'full_name',
        'email',
        'phone',
        'password',
        'api_key',
    ];

    protected $hidden = [
        'password',
        'api_key',
    ];


}
