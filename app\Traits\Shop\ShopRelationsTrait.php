<?php

namespace App\Traits\Shop;

use App\Models\User\User;
use MongoDB\Laravel\Relations\BelongsToMany;

/**
 * Shop Relations Trait
 *
 * This trait contains all relationship methods for the Shop model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Shop
 */
trait ShopRelationsTrait
{
    /**
     * Get the users that belong to this shop.
     *
     * @return \MongoDB\Laravel\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'shop_user', 'shop_id', 'user_id');
    }
}
