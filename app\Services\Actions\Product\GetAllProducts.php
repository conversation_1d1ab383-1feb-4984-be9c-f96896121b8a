<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use Illuminate\Database\Eloquent\Builder;

/**
 * Action class to retrieve all products with filtering, sorting, and pagination.
 */
class GetAllProducts
{
    private bool $hasTextSearch = false;

    /**
     * Handle the product listing request with filters and pagination.
     *
     * @param array $data The validated request data
     * @return array Contains paginated products, total count, min/max prices
     */
    public function handle(array $data): array
    {
        // Start with a clean base query
        $query = $this->buildBaseQuery();

        // Apply filters incrementally
        $this->applySearchFilter($query, $data);
        $this->applyPriceRangeFilter($query, $data);
        $this->applyStockFilter($query, $data);
        $this->applyGuaranteeFilter($query, $data);
        $this->applyCategoryFilter($query, $data);
        $this->applyShopFilter($query, $data);

        // Apply sorting
        $this->applySorting($query, $data['sort'] ?? 'newest');

        // Get total count before pagination
        $totalCount = $query->count();

        // Get min and max prices (optimized calculation)
        $priceStats = $this->getPriceStatistics();

        // Apply pagination
        $perPage = $data['per_page'] ?? 15;
        $page = $data['page'] ?? 1;
        $products = $query->paginate($perPage, ['*'], 'page', $page);

        return [
            'products' => $products,
            'total_count' => $totalCount,
            'min_price' => $priceStats['min_price'],
            'max_price' => $priceStats['max_price'],
        ];
    }

    /**
     * Build the base query with essential relationships.
     *
     * @return Builder
     */
    private function buildBaseQuery(): Builder
    {
        return Product::with(['variations', 'gallery', 'guarantees']);
    }

    /**
     * Apply search filter with text search and relevance scoring.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applySearchFilter(Builder $query, array $data): void
    {
        if (!empty($data['search'])) {
            $this->hasTextSearch = true;

            $query->where('$text', ['$search' => $data['search']]);

            $query->project([
                '*',
                'score' => ['$meta' => 'textScore']
            ]);
        }
    }

    /**
     * Apply price range filter considering both regular and sale prices.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyPriceRangeFilter(Builder $query, array $data): void
    {
        if (!isset($data['min_price']) && !isset($data['max_price'])) {
            return;
        }

        $query->whereHas('variations', function ($variationQuery) use ($data) {
            if (isset($data['min_price'])) {
                $minPrice = (int) $data['min_price'];
                $variationQuery->where(function ($priceQuery) use ($minPrice) {
                    // Check if sale_price meets minimum (when sale_price exists and is lower than regular price)
                    $priceQuery->where(function ($saleQuery) use ($minPrice) {
                        $saleQuery->whereNotNull('sale_price')
                            ->whereColumn('sale_price', '<', 'price')
                            ->where('sale_price', '>=', $minPrice);
                    })
                    // OR regular price meets minimum (when no valid sale_price)
                    ->orWhere(function ($regularQuery) use ($minPrice) {
                        $regularQuery->where(function ($noSaleQuery) use ($minPrice) {
                            $noSaleQuery->whereNull('sale_price')
                                ->orWhereColumn('sale_price', '>=', 'price');
                        })->where('price', '>=', $minPrice);
                    });
                });
            }

            if (isset($data['max_price'])) {
                $maxPrice = (int) $data['max_price'];
                $variationQuery->where(function ($priceQuery) use ($maxPrice) {
                    // Check if sale_price meets maximum (when sale_price exists and is lower than regular price)
                    $priceQuery->where(function ($saleQuery) use ($maxPrice) {
                        $saleQuery->whereNotNull('sale_price')
                            ->whereColumn('sale_price', '<', 'price')
                            ->where('sale_price', '<=', $maxPrice);
                    })
                    // OR regular price meets maximum (when no valid sale_price)
                    ->orWhere(function ($regularQuery) use ($maxPrice) {
                        $regularQuery->where(function ($noSaleQuery) use ($maxPrice) {
                            $noSaleQuery->whereNull('sale_price')
                                ->orWhereColumn('sale_price', '>=', 'price');
                        })->where('price', '<=', $maxPrice);
                    });
                });
            }
        });
    }

    /**
     * Apply stock availability filter using the ledger-based inventory system.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyStockFilter(Builder $query, array $data): void
    {
        if (empty($data['in_stock_only']) || $data['in_stock_only'] !== 'true') {
            return;
        }

        $query->whereHas('variations', function ($variationQuery) {
            // Use the getCurrentQuantityAttribute logic to check for stock
            // This checks if the sum of purchase entries minus reservations is greater than 0
            $variationQuery->whereHas('purchases', function ($purchaseQuery) {
                // At least one purchase entry exists (indicating the product has been stocked)
                $purchaseQuery->selectRaw('SUM(quantity) as total_quantity')
                    ->havingRaw('SUM(quantity) > 0');
            });
        });
    }

    /**
     * Apply guarantee availability filter.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyGuaranteeFilter(Builder $query, array $data): void
    {
        if (empty($data['has_guarantee_only']) || $data['has_guarantee_only'] !== 'true') {
            return;
        }

        $query->whereHas('guarantees');
    }

    /**
     * Apply category filter (placeholder for future implementation).
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyCategoryFilter(Builder $query, array $data): void
    {
        if (empty($data['category_id'])) {
            return;
        }

        // TODO: Implement category filtering when category relationships are established
        // $query->whereHas('categories', function ($categoryQuery) use ($data) {
        //     $categoryQuery->where('_id', $data['category_id']);
        // });

        // Suppress unused variable warning for now
        unset($query);
    }

    /**
     * Apply shop filter (placeholder for future implementation).
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyShopFilter(Builder $query, array $data): void
    {
        if (empty($data['shop_id'])) {
            return;
        }

        $query->where('shop_id', $data['shop_id']);
    }

    /**
     * Apply sorting to the query based on the sort parameter.
     *
     * @param Builder $query
     * @param string $sort
     * @return void
     */
    private function applySorting(Builder $query, string $sort): void
    {
        switch ($sort) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'cheapest':
                $this->applyCheapestSort($query);
                break;
            case 'most_expensive':
                $this->applyMostExpensiveSort($query);
                break;
            case 'most_sales':
                $this->applyMostSalesSort($query);
                break;
            case 'most_viewed':
                // TODO: Implement view tracking and sorting
                $query->orderBy('created_at', 'desc');
                break;
            case 'most_popular':
                $this->applyMostPopularSort($query);
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        // Add text search relevance as secondary sort if applicable
        if ($this->hasTextSearch) {
            // For MongoDB text search, add relevance score ordering
            $query->orderBy('score', 'desc');
        }
    }

    /**
     * Apply cheapest price sorting.
     *
     * @param Builder $query
     * @return void
     */
    private function applyCheapestSort(Builder $query): void
    {
        // For MongoDB, we need to use aggregation to sort by minimum effective price
        // This is a simplified approach - in production, consider using aggregation pipeline
        $query->orderBy('created_at', 'desc'); // Fallback for now

        // TODO: Implement proper aggregation-based sorting
        // $query->orderByRaw([
        //     ['$min' => [
        //         '$cond' => [
        //             'if' => ['$and' => [
        //                 ['$ne' => ['$variations.sale_price', null]],
        //                 ['$lt' => ['$variations.sale_price', '$variations.price']]
        //             ]],
        //             'then' => '$variations.sale_price',
        //             'else' => '$variations.price'
        //         ]
        //     ]] => 1
        // ]);
    }

    /**
     * Apply most expensive price sorting.
     *
     * @param Builder $query
     * @return void
     */
    private function applyMostExpensiveSort(Builder $query): void
    {
        // Similar to cheapest, but in descending order
        $query->orderBy('created_at', 'desc'); // Fallback for now

        // TODO: Implement proper aggregation-based sorting for max price
    }

    /**
     * Apply most sales sorting using invoice products as proxy.
     *
     * @param Builder $query
     * @return void
     */
    private function applyMostSalesSort(Builder $query): void
    {
        // Use comment count as a proxy for sales popularity
        // TODO: Implement proper sales tracking using InvoiceProduct model
        $query->withCount('comments')->orderBy('comments_count', 'desc');
    }

    /**
     * Apply most popular sorting using multiple metrics.
     *
     * @param Builder $query
     * @return void
     */
    private function applyMostPopularSort(Builder $query): void
    {
        // Use comment count as popularity metric
        // TODO: Combine with view count, sales count, and rating when available
        $query->withCount('comments')->orderBy('comments_count', 'desc');
    }

    /**
     * Get minimum and maximum prices from all product variations (optimized).
     *
     * @return array
     */
    private function getPriceStatistics(): array
    {
        // Use aggregation to get min/max prices efficiently
        $stats = ProductVariation::selectRaw('
            MIN(CASE
                WHEN sale_price IS NOT NULL AND sale_price < price
                THEN sale_price
                ELSE price
            END) as min_price,
            MAX(CASE
                WHEN sale_price IS NOT NULL AND sale_price < price
                THEN sale_price
                ELSE price
            END) as max_price
        ')->first();

        return [
            'min_price' => (int) ($stats->min_price ?? 0),
            'max_price' => (int) ($stats->max_price ?? 0),
        ];
    }
}
