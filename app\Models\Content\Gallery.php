<?php

namespace App\Models\Content;

use App\Traits\Gallery\GalleryRelationsTrait;
use MongoDB\Laravel\Eloquent\Model;

class Gallery extends Model
{
    use GalleryRelationsTrait;
    protected $connection = 'mongodb';
    protected $collection = 'galleries';

    protected $fillable = [
        'image_url',
        'caption',
    ];

    protected $casts = [
        'imageable_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


}
