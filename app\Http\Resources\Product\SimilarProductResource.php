<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Product models into simplified API responses for similar products.
 *
 * Provides a minimal representation of a product with only the essential data:
 * - First gallery image
 * - Slug
 * - Rate
 * - Price (uses sale_price if available, otherwise regular price)
 */
class SimilarProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Get the first gallery image if available
        $firstImage = null;
        if ($this->relationLoaded('gallery') && $this->gallery->isNotEmpty()) {
            $firstImage = [
                'url' => $this->gallery->first()->image_url,
                'caption' => $this->gallery->first()->caption,
            ];
        }

        // Get the default variant for price
        $price = null;
        if ($this->relationLoaded('variations') && $this->variations->isNotEmpty()) {
            // Sort variations by quantity (descending) and then by price (ascending)
            $defaultVariant = $this->variations->sortBy([
                ['current_quantity', 'desc'],
                ['price', 'asc']
            ])->first();

            if ($defaultVariant) {
                // If sale price exists and is not null, use it as the price; otherwise use regular price
                $price = isset($defaultVariant->sale_price) && $defaultVariant->sale_price !== null
                    ? $defaultVariant->sale_price
                    : $defaultVariant->price;
            }
        } else {
            $price = 0;
        }

        // Use stored product rating
        $productAverageRate = $this->rating ?? 0;

        return [
            'title' => $this->title,
            'slug' => $this->slug,
            'image' => $firstImage,
            'rate' => $productAverageRate,
            'price' => $price,
        ];
    }
}
