<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mongodb')->create('keywords', function (Blueprint $collection) {
            $collection->string('title');
            $collection->string('keywordable_type'); // e.g., "App\Models\Product"
            $collection->string('keywordable_id');   // product _id
            $collection->timestamps();
        });
    }

    public function down(): void
    {
        Schema::connection('mongodb')->dropIfExists('keywords');
    }
};
