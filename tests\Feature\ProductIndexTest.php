<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

/**
 * Test class for Product Index API endpoint
 */
class ProductIndexTest extends TestCase
{
    /**
     * Test that the product index endpoint returns a successful response
     *
     * @return void
     */
    public function test_product_index_returns_successful_response()
    {
        $response = $this->get('/api/v1/products');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'pagination' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total',
                    'from',
                    'to',
                ],
                'total_products',
                'min_price',
                'max_price',
                'products' => [
                    '*' => [
                        'title',
                        'slug',
                        'rate',
                        'image',
                        'price',
                        'sale_price',
                    ]
                ]
            ]
        ]);
    }

    /**
     * Test product index with search parameter
     *
     * @return void
     */
    public function test_product_index_with_search()
    {
        $response = $this->get('/api/v1/products?search=test');

        $response->assertStatus(200);
    }

    /**
     * Test product index with price range
     *
     * @return void
     */
    public function test_product_index_with_price_range()
    {
        $response = $this->get('/api/v1/products?min_price=1000&max_price=5000');

        $response->assertStatus(200);
    }

    /**
     * Test product index with sorting
     *
     * @return void
     */
    public function test_product_index_with_sorting()
    {
        $response = $this->get('/api/v1/products?sort=newest');

        $response->assertStatus(200);
    }

    /**
     * Test product index with filters
     *
     * @return void
     */
    public function test_product_index_with_filters()
    {
        $response = $this->get('/api/v1/products?in_stock_only=true&has_guarantee_only=true');

        $response->assertStatus(200);
    }

    /**
     * Test product index with pagination
     *
     * @return void
     */
    public function test_product_index_with_pagination()
    {
        $response = $this->get('/api/v1/products?page=1&per_page=10');

        $response->assertStatus(200);
    }

    /**
     * Test product index with invalid sort parameter
     *
     * @return void
     */
    public function test_product_index_with_invalid_sort()
    {
        $response = $this->get('/api/v1/products?sort=invalid_sort');

        // Laravel returns 302 for validation errors in this setup
        $response->assertStatus(302);
    }

    /**
     * Test product index with invalid price range
     *
     * @return void
     */
    public function test_product_index_with_invalid_price_range()
    {
        $response = $this->get('/api/v1/products?min_price=5000&max_price=1000');

        // Laravel returns 302 for validation errors in this setup
        $response->assertStatus(302);
    }
}
