<?php

namespace App\Console\Commands;

use App\Models\Shopping\Invoice;
use App\Models\Shopping\Transaction;
use App\Models\User\WalletTransaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RejectExpiredInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reject-expired-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rejects invoices that are still pending 30 minutes after creation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to reject expired invoices...');
        Log::channel('scheduled')->info('Starting RejectExpiredInvoices command');

        try {
            // Find invoices that are still pending and were created more than 30 minutes ago
            $cutoffTime = now()->subMinutes(1);

            DB::transaction(function () use ($cutoffTime) {
                // Get invoices with at least one wallet transaction
                $expiredInvoices = Invoice::where('status', 'pending')
                    ->where('created_at', '<', $cutoffTime)
                    // Must have at least one 'wallet' transaction
                    ->whereHas('transactions', function ($query) {
                        $query->where('payment_method', 'wallet');
                    })
                    // Must have more than one transaction in total
                    ->has('transactions', '>', 1)
                    // Eager load all transactions
                    ->with('transactions')
                    ->get();


                $count = $expiredInvoices->count();
                Log::channel('scheduled')->info("Found {$count} expired invoices to reject");
                $this->info("Found {$count} expired invoices to reject");

                foreach ($expiredInvoices as $invoice) {
                    // Process refunds for any payments associated with this invoice
                    $this->processRefunds($invoice);

                    // Update invoice status to rejected
                    $invoice->update(['status' => 'rejected']);

                    Log::channel('scheduled')->info("Rejected invoice ID: {$invoice->_id}, created at: {$invoice->created_at}");
                }
            });

            $this->info('Successfully rejected expired invoices and processed refunds');
            Log::channel('scheduled')->info('Completed RejectExpiredInvoices command with refund processing');

            return 0; // Success
        } catch (\Exception $e) {
            $this->error('Error rejecting expired invoices: ' . $e->getMessage());
            Log::channel('scheduled')->error('Error in RejectExpiredInvoices command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1; // Error
        }
    }

    /**
     * Process refunds for wallet transactions associated with a rejected invoice
     *
     * Only handles wallet transactions, creating WalletTransaction records to credit the user's wallet.
     *
     * @param Invoice $invoice The invoice to process refunds for
     * @return void
     */
    private function processRefunds(Invoice $invoice): void
    {
        try {
            // If there are no transactions, nothing to refund
            if ($invoice->transactions->isEmpty()) {
                return;
            }

            Log::channel('scheduled')->info("Processing wallet refunds for invoice ID: {$invoice->_id} with " . $invoice->transactions->count() . " wallet transactions");

            // Process each wallet transaction
            foreach ($invoice->transactions as $transaction) {
                if ($transaction->isWalletPayment) {
                    $this->processWalletRefund($transaction);
                    Log::channel('scheduled')->info("Refunded wallet transaction ID: {$transaction->_id}, amount: {$transaction->amount}");
                }


            }
        } catch (\Exception $e) {
            // Log the error but continue processing
            Log::channel('scheduled')->error("Error processing refunds for invoice ID: {$invoice->_id}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Process refund for wallet payments by creating a WalletTransaction record
     *
     * @param Transaction $transaction The transaction to refund
     * @return void
     */
    private function processWalletRefund(Transaction $transaction): void
    {
        // Create a new wallet transaction to credit the user's wallet
        WalletTransaction::create([
            'user_id' => $transaction->user_id,
            'type' => WalletTransaction::TYPE_DEPOSIT,
            'amount' => $transaction->amount,
            'referenceable_id' => $transaction->payable_id,
            'referenceable_type' => $transaction->payable_type,
            'description' => __('messages.transaction.wallet_refund_for_rejected_invoice', ['invoice_id' => $transaction->payable_id]),
        ]);

        Log::channel('scheduled')->info("Created wallet refund for user ID: {$transaction->user_id}, amount: {$transaction->amount}");
    }
}
