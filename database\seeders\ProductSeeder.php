<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use App\Models\User\Shop;
use Illuminate\Support\Str;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;

/**
 * Seeder for creating sample products in the database.
 */
class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates 8 products with Persian names and descriptions, each with:
     * - Random product category
     * - Random shop assignment
     * - 3 random gallery images
     */
    public function run(): void
    {
        // 6 placeholder image URLs
        $imageUrls = [
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
            'https://picsum.photos/800/400',
        ];

        // Persian product categories
        $productCategories = [
            'گوشی موبایل',
            'لپ تاپ',
            'تبلت',
            'ساعت هوشمند',
            'هدفون',
            'لوازم جانبی',
            'کنسول بازی',
            'دوربین',
            'لوازم خانگی',
            'کتاب'
        ];

        // Get all shops
        $shops = Shop::all();

        if ($shops->isEmpty()) {
            $this->command->warn('No shops found. Make sure to run ShopSeeder first.');
            return;
        }

        // Create 8 products with Persian names and descriptions
        for ($i = 0; $i < 8; $i++) {
            // Generate a Persian product name
            $productCategory = $productCategories[array_rand($productCategories)];
            $word = Faker::word();
            $name = "{$productCategory} {$word}";
            $slug = Str::slug($name);

            // Generate Persian descriptions
            $description = Faker::paragraph();
            $metaDescription = Faker::sentence();

            // Randomly select a shop
            $shop = $shops->random();

            $product = Product::firstOrCreate(
                ['title' => $name],
                [
                    'description' => $description,
                    'slug' => $slug,
                    'meta_title' => "{$name} | خرید آنلاین",
                    'meta_description' => $metaDescription,
                    'shop_id' => $shop->_id,
                ]
            );

            // Randomly pick 3 images (no duplicates)
            foreach (collect($imageUrls)->shuffle()->take(3) as $url) {
                $product->gallery()->create([
                    'image_url' => $url,
                    'caption' => "تصویر {$product->title}",
                ]);
            }
        }
    }
}
