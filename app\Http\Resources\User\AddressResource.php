<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Address Resource
 *
 * Transforms Address model to a standardized API response
 */
class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (string) $this->_id,
            'name' => $this->name,
            'receiver_name' => $this->receiver_name,
            'receiver_phone' => $this->receiver_phone,
            'is_recipient_self' => $this->is_recipient_self,
            'province' => $this->province,
            'city' => $this->city,
            'zip_code' => $this->zip_code,
            'address' => $this->address,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ];
    }
}
