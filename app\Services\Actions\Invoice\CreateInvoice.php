<?php

namespace App\Services\Actions\Invoice;

use App\Models\Product\ProductsReservation;
use App\Models\Shopping\Invoice;
use App\Models\Shopping\InvoiceProduct;
use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use App\Models\Shopping\ShoppingCart;
use App\Models\User\Address;
use DB;

/**
 * Action class for creating a new invoice.
 */
class CreateInvoice
{
    /**
     * Create a new invoice for a user.
     * Creates invoice products from cart items with their details.
     * Clears the cart after successful invoice creation.
     *
     * @param array $data An array containing the address_id
     * @return Invoice The created invoice with loaded products
     */
    public function handle(array $data): Invoice
    {
        $invoice = null;
        DB::transaction(function () use ($data, &$invoice) {
            // Get the authenticated user's ID directly
            $userId = auth()->id();

            // Get the user's cart - validation is already done in the Form Request
            $cart = ShoppingCart::where('user_id', $userId)->first();
            $address = Address::find($data['address_id']);
            $cartItems = $cart->items;

            // Create the invoice with pending status
            $invoice = Invoice::create([
                'user_id' => $userId,
                'status' => 'pending',
                'address' => $address['address'],
                'receiver_name' => $address['receiver_name'],
                'receiver_phone' => $address['receiver_phone'],
                'province' => $address['province'],
                'city' => $address['city'],
                'zip_code' => $address['zip_code'],
                'latitude' => $address['latitude'] ?? null,
                'longitude' => $address['longitude'] ?? null,
            ]);

            // Create invoice products from cart items
            foreach ($cartItems as $cartItem) {
                try {
                    // Create invoice product
                    $invoiceProduct = $invoice->products()->create([
                        'product_id' => $cartItem->product_id,
                        'variant_id' => $cartItem->variant_id,
                        'name' => $cartItem->name,
                        'price' => $cartItem->price,
                        'sale_price' => $cartItem->sale_price && $cartItem->sale_price < $cartItem->price ? $cartItem->sale_price : null,
                        'quantity' => $cartItem->quantity,
                        'image' => $cartItem->image,
                    ]);

                    // Get the variant with its attributes
                    $variant = ProductVariation::with('attributes')->find($cartItem->variant_id);

                    // Save variant attributes to invoice product details
                    foreach ($variant->attributes as $attribute) {
                        $invoiceProduct->details()->create([
                            'key' => $attribute->attribute_title,
                            'value' => $attribute->attribute_value,
                        ]);
                    }

                    // Save product details to invoice product details
                    $product = Product::with('details')->find($cartItem->product_id);
                    if ($product) {
                        foreach ($product->details as $detail) {
                            $invoiceProduct->details()->create([
                                'key' => $detail->key,
                                'value' => $detail->value,
                            ]);
                        }
                    }


                    ProductsReservation::create([
                        'user_id' => $cart->user_id,
                        'invoice_id' => $invoice->_id,
                        'variant_id' => $cartItem->variant_id,
                        'quantity' => $cartItem->quantity,
                        'status' => 'pending',
                        'expire_date' => now()->addMinute(),
                    ]);
                } catch (\Exception $e) {
                    // If there's an error with one item, delete the invoice and throw the exception
                    $invoice->delete();
                    throw $e;
                }
            }

            // Clear the cart
            $cart->items()->delete();
        });
        // Load the products relationship with their details
        $invoice->load('products.details');
        // Create product reservations

        return $invoice;
    }
}
