<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\Product;
use <PERSON><PERSON><PERSON><PERSON>\Faker\Facades\Faker;
use App\Models\User\User;
/**
 * Seeder for creating comments on products.
 */
class ProductCommentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Creates 8 comments for each product with:
     * - Random comment body (short or long)
     * - Random rating (1-5)
     * - Random has_bought status
     * - Random user assignment (if users exist)
     * - Default IP address and user agent
     */
    public function run(): void
    {
        $products = Product::all();
        $users = User::all();

        foreach ($products as $product) {
            // Create 8 comments for each product
            for ($i = 0; $i < 8; $i++) {
                // Randomly decide if this will be a short or long comment
                $body = rand(0, 1) === 1 ? Faker::sentence() : Faker::paragraph();

                // Generate random rating between 1 and 5
                $rate = rand(1, 5);

                // Randomly decide if the user has bought the product
                $hasBought = (bool) rand(0, 1);

                // Randomly select a user for the comment
                $userId = $users->isNotEmpty() ? $users->random()->_id : null;

                $product->comments()->create([
                    'body' => $body,
                    'rate' => $rate,
                    'has_bought' => $hasBought,
                    'user_id' => $userId,
                    'ip_address' => '127.0.0.1',
                    'user_agent' => 'Seeder/1.0',
                ]);
            }
        }
    }
}