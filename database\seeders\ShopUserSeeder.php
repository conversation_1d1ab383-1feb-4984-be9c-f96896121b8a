<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User\User;
use App\Models\User\Shop;

class ShopUserSeeder extends Seeder
{
    public function run(): void
    {
        $sellers = User::where('role', 'seller')->get();
        $shops = Shop::all();

        if ($sellers->isEmpty() || $shops->isEmpty()) {
            return;
        }

        foreach ($sellers as $seller) {
            $shop = $shops->random();

            $shop->users()->syncWithoutDetaching([$seller->_id]);
        }
    }
}
